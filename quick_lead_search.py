#!/usr/bin/env python3
"""
Quick Lead Search - Simplified version for immediate results
"""

import time
import random
import json
from datetime import datetime
from targeted_lead_generator import TargetedLeadGenerator

def quick_search_keynote_speakers(max_leads=5):
    """Quick search for keynote speakers"""
    print("TARGETED LEAD SEARCH - KEYNOTE SPEAKERS")
    print("=" * 50)
    print("Criteria:")
    print("• 100-10,000 followers")
    print("• US-based profiles")
    print("• Recent activity")
    print("• Public accounts")
    print()
    
    generator = TargetedLeadGenerator()
    
    # Search for keynote speakers
    print("Step 1: Searching Google for keynote speaker profiles...")
    usernames = generator.search_google_for_industry('keynote_speaker', num_results=20)
    
    if not usernames:
        print("No usernames found. Try again later.")
        return []
    
    print(f"Found {len(usernames)} potential usernames")
    print("Sample usernames:", usernames[:5])
    print()
    
    # Verify and qualify profiles
    print("Step 2: Verifying profiles and applying qualification criteria...")
    qualified_leads = []
    
    for i, username in enumerate(usernames[:10], 1):  # Check first 10 to avoid rate limits
        print(f"[{i}/10] Checking @{username}...")
        
        try:
            qualified_profile = generator.verify_and_qualify_profile(username)
            
            if qualified_profile:
                qualified_leads.append(qualified_profile)
                score = qualified_profile.get('qualification_score', 0)
                followers = qualified_profile.get('followers', 0)
                contact = qualified_profile.get('contact_info', {})
                email = contact.get('email', 'None')
                
                print(f"   QUALIFIED! Score: {score}/100, Followers: {followers:,}, Email: {email}")
                
                if len(qualified_leads) >= max_leads:
                    print(f"   Found {max_leads} qualified leads - stopping search")
                    break
            else:
                print(f"   Not qualified")
                
        except Exception as e:
            print(f"   Error: {e}")
            continue
    
    print()
    print("=" * 50)
    print(f"RESULTS: Found {len(qualified_leads)} qualified keynote speaker leads")
    print("=" * 50)
    
    # Display results
    for i, lead in enumerate(qualified_leads, 1):
        contact = lead.get('contact_info', {})
        print(f"\n{i}. @{lead['username']}")
        print(f"   Name: {lead.get('full_name', 'N/A')}")
        print(f"   Followers: {lead.get('followers', 0):,}")
        print(f"   Score: {lead.get('qualification_score', 0)}/100")
        print(f"   Email: {contact.get('email', 'None')}")
        print(f"   Website: {contact.get('website', 'None')}")
        print(f"   Bio: {lead.get('biography', '')[:80]}...")
        print(f"   Profile: https://instagram.com/{lead['username']}")
    
    # Save results
    if qualified_leads:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"keynote_speakers_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(qualified_leads, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\nResults saved to: {filename}")
    
    return qualified_leads

def quick_search_business_coaches(max_leads=5):
    """Quick search for business coaches"""
    print("\nTARGETED LEAD SEARCH - BUSINESS COACHES")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    # Search for coaches
    print("Step 1: Searching Google for business coach profiles...")
    usernames = generator.search_google_for_industry('coach_consultant_creator', num_results=20)
    
    if not usernames:
        print("No usernames found. Try again later.")
        return []
    
    print(f"Found {len(usernames)} potential usernames")
    print()
    
    # Verify and qualify profiles
    print("Step 2: Verifying profiles...")
    qualified_leads = []
    
    for i, username in enumerate(usernames[:10], 1):
        print(f"[{i}/10] Checking @{username}...")
        
        try:
            qualified_profile = generator.verify_and_qualify_profile(username)
            
            if qualified_profile:
                qualified_leads.append(qualified_profile)
                score = qualified_profile.get('qualification_score', 0)
                followers = qualified_profile.get('followers', 0)
                
                print(f"   QUALIFIED! Score: {score}/100, Followers: {followers:,}")
                
                if len(qualified_leads) >= max_leads:
                    break
            else:
                print(f"   Not qualified")
                
        except Exception as e:
            print(f"   Error: {e}")
            continue
    
    print(f"\nFound {len(qualified_leads)} qualified business coach leads")
    
    # Display results
    for i, lead in enumerate(qualified_leads, 1):
        contact = lead.get('contact_info', {})
        print(f"\n{i}. @{lead['username']}")
        print(f"   Followers: {lead.get('followers', 0):,}")
        print(f"   Score: {lead.get('qualification_score', 0)}/100")
        print(f"   Email: {contact.get('email', 'None')}")
    
    return qualified_leads

def main():
    """Run quick lead searches"""
    print("QUICK INSTAGRAM LEAD GENERATION")
    print("=" * 60)
    print("Finding qualified leads in your target industries")
    print()
    
    all_leads = []
    
    try:
        # Search for keynote speakers
        speaker_leads = quick_search_keynote_speakers(max_leads=3)
        all_leads.extend(speaker_leads)
        
        # Small delay between searches
        if speaker_leads:
            print("\nWaiting 10 seconds before next search...")
            time.sleep(10)
        
        # Search for business coaches
        coach_leads = quick_search_business_coaches(max_leads=3)
        all_leads.extend(coach_leads)
        
        # Final summary
        print("\n" + "=" * 60)
        print("FINAL RESULTS")
        print("=" * 60)
        print(f"Total qualified leads found: {len(all_leads)}")
        print(f"Keynote speakers: {len(speaker_leads)}")
        print(f"Business coaches: {len(coach_leads)}")
        
        if all_leads:
            # Calculate stats
            total_followers = sum(lead.get('followers', 0) for lead in all_leads)
            avg_followers = total_followers // len(all_leads) if all_leads else 0
            with_email = sum(1 for lead in all_leads if lead.get('contact_info', {}).get('email'))
            avg_score = sum(lead.get('qualification_score', 0) for lead in all_leads) // len(all_leads)
            
            print(f"\nLead Quality Stats:")
            print(f"• Average followers: {avg_followers:,}")
            print(f"• Average qualification score: {avg_score}/100")
            print(f"• Leads with email: {with_email}/{len(all_leads)}")
            
            print(f"\nTop 3 Highest Scoring Leads:")
            sorted_leads = sorted(all_leads, key=lambda x: x.get('qualification_score', 0), reverse=True)
            
            for i, lead in enumerate(sorted_leads[:3], 1):
                contact = lead.get('contact_info', {})
                print(f"{i}. @{lead['username']} (Score: {lead.get('qualification_score', 0)}/100)")
                print(f"   Followers: {lead.get('followers', 0):,}")
                print(f"   Email: {contact.get('email', 'None')}")
                print(f"   Industry: {lead.get('classified_industry', '').replace('_', ' ').title()}")
            
            print(f"\nSUCCESS! You now have {len(all_leads)} qualified leads ready for outreach!")
            print(f"Focus on leads with scores 60+ and email contacts for best results.")
        else:
            print("\nNo qualified leads found in this search.")
            print("Try running again later or adjusting search criteria.")
    
    except KeyboardInterrupt:
        print("\nSearch interrupted by user")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
