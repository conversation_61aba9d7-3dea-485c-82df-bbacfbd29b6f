#!/usr/bin/env python3
"""
Instagram Username Search Tool

This script demonstrates how to use the UsernameSearcher class to:
1. Search for Instagram profiles based on keywords
2. Verify and filter profiles based on criteria
3. Find available username variations
4. Export results to Excel and JSON

Usage examples:
    python run_username_search.py --mode search --keywords "keynote speaker" "business coach"
    python run_username_search.py --mode availability --names "john smith" "jane doe"
    python run_username_search.py --mode both --keywords "author" --names "writer pro"
"""

import argparse
import logging
from typing import List
from username_searcher import UsernameSearcher

def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('username_search.log'),
            logging.StreamHandler()
        ]
    )

def search_existing_profiles(searcher: UsernameSearcher, keywords: List[str], num_results: int = 50):
    """Search for existing Instagram profiles"""
    print(f"\n🔍 Searching for existing profiles with keywords: {keywords}")
    
    # Define search criteria
    criteria = {
        'min_followers': 100,           # Minimum 100 followers
        'max_followers': 100000,        # Maximum 100k followers
        'exclude_private': True,        # Exclude private accounts
        'min_posts': 5,                # Minimum 5 posts
        'target_industries': [          # Target specific industries
            'speaker', 'coach', 'author', 'course_creator', 'business'
        ],
        'require_email': False          # Don't require email (optional)
    }
    
    # Search and verify profiles
    profiles = searcher.search_and_verify_usernames(keywords, criteria, num_results)
    
    if profiles:
        print(f"\n✅ Found {len(profiles)} matching profiles!")
        
        # Show sample results
        print("\n📋 Sample Results:")
        for i, profile in enumerate(profiles[:5], 1):
            print(f"{i}. @{profile['username']}")
            print(f"   Name: {profile.get('full_name', 'N/A')}")
            print(f"   Industry: {profile.get('classified_industry', 'N/A')}")
            print(f"   Followers: {profile.get('followers', 0):,}")
            print(f"   Email: {profile.get('email', 'N/A')}")
            print(f"   Bio: {profile.get('biography', 'N/A')[:100]}...")
            print()
        
        # Export results
        excel_file = searcher.export_to_excel(profiles)
        json_file = searcher.export_to_json(profiles)
        
        print(f"📊 Results exported to:")
        print(f"   Excel: {excel_file}")
        print(f"   JSON: {json_file}")
        
        return profiles
    else:
        print("❌ No profiles found matching the criteria")
        return []

def check_username_availability(searcher: UsernameSearcher, base_names: List[str], industry: str = None):
    """Check availability of username variations"""
    print(f"\n🔍 Checking username availability for: {base_names}")
    if industry:
        print(f"   Industry context: {industry}")
    
    available_usernames = searcher.find_available_usernames(
        base_names, 
        industry=industry, 
        max_variations=15
    )
    
    if available_usernames:
        print(f"\n✅ Found {len(available_usernames)} available usernames!")
        
        # Group by base name
        by_base_name = {}
        for username_data in available_usernames:
            base = username_data['base_name']
            if base not in by_base_name:
                by_base_name[base] = []
            by_base_name[base].append(username_data['username'])
        
        print("\n📋 Available Usernames:")
        for base_name, usernames in by_base_name.items():
            print(f"\nBase: '{base_name}'")
            for username in usernames[:10]:  # Show first 10
                print(f"   ✅ @{username}")
        
        # Export availability results
        excel_file = searcher.export_to_excel(available_usernames, "available_usernames.xlsx")
        json_file = searcher.export_to_json(available_usernames, "available_usernames.json")
        
        print(f"\n📊 Availability results exported to:")
        print(f"   Excel: {excel_file}")
        print(f"   JSON: {json_file}")
        
        return available_usernames
    else:
        print("❌ No available usernames found")
        return []

def main():
    parser = argparse.ArgumentParser(description='Instagram Username Search Tool')
    parser.add_argument('--mode', choices=['search', 'availability', 'both'], 
                       default='both', help='Search mode')
    parser.add_argument('--keywords', nargs='+', 
                       default=['keynote speaker', 'business coach', 'online course creator'],
                       help='Keywords to search for existing profiles')
    parser.add_argument('--names', nargs='+',
                       default=['john smith', 'jane doe', 'alex johnson'],
                       help='Base names to check availability for')
    parser.add_argument('--industry', 
                       choices=['speaker', 'coach', 'author', 'course_creator', 'business'],
                       help='Industry context for username generation')
    parser.add_argument('--num-results', type=int, default=50,
                       help='Number of results per keyword (default: 50)')
    parser.add_argument('--use-proxies', action='store_true',
                       help='Enable proxy rotation (requires proxy list)')
    parser.add_argument('--proxy-file', 
                       help='File containing proxy list (one per line)')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Load proxies if specified
    proxy_list = []
    if args.use_proxies and args.proxy_file:
        try:
            with open(args.proxy_file, 'r') as f:
                proxy_list = [line.strip() for line in f if line.strip()]
            print(f"📡 Loaded {len(proxy_list)} proxies")
        except FileNotFoundError:
            print(f"❌ Proxy file not found: {args.proxy_file}")
            return
    
    # Initialize searcher
    print("🚀 Initializing Username Searcher...")
    searcher = UsernameSearcher(
        use_proxies=args.use_proxies,
        proxy_list=proxy_list
    )
    
    results = {}
    
    # Search for existing profiles
    if args.mode in ['search', 'both']:
        results['existing_profiles'] = search_existing_profiles(
            searcher, args.keywords, args.num_results
        )
    
    # Check username availability
    if args.mode in ['availability', 'both']:
        results['available_usernames'] = check_username_availability(
            searcher, args.names, args.industry
        )
    
    # Summary
    print("\n" + "="*60)
    print("📊 SEARCH SUMMARY")
    print("="*60)
    
    if 'existing_profiles' in results:
        print(f"Existing Profiles Found: {len(results['existing_profiles'])}")
    
    if 'available_usernames' in results:
        print(f"Available Usernames Found: {len(results['available_usernames'])}")
    
    print("\n✅ Search completed successfully!")
    print("📁 Check the generated Excel and JSON files for detailed results.")

if __name__ == "__main__":
    main()
