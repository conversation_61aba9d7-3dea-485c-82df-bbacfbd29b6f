#!/usr/bin/env python3
"""
Test Targeted Lead Generation System
Demonstrates the qualification criteria and lead scoring
"""

import re
from datetime import datetime
from targeted_lead_generator import TargetedLeadGenerator

def test_us_location_detection():
    """Test US location detection with sample profiles"""
    print("🇺🇸 Testing US Location Detection")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    test_profiles = [
        {
            'biography': 'Business coach based in California helping entrepreneurs grow',
            'external_url': 'https://mycoaching.com',
            'full_name': '<PERSON>'
        },
        {
            'biography': 'Keynote speaker | Available nationwide | Book me for your next event',
            'external_url': '',
            'full_name': '<PERSON>'
        },
        {
            'biography': 'Author living in NYC | Published 3 books on leadership',
            'external_url': 'https://author.com',
            'full_name': 'Jane <PERSON>'
        },
        {
            'biography': 'Life coach from London helping people transform their lives',
            'external_url': 'https://uk-coaching.co.uk',
            'full_name': 'British Coach'
        },
        {
            'biography': 'Consultant | Texas-based | Helping startups scale',
            'external_url': '',
            'full_name': 'Tom Consultant'
        }
    ]
    
    for i, profile in enumerate(test_profiles, 1):
        is_us = generator.is_us_based(profile)
        status = "✅ US-based" if is_us else "❌ Not US-based"
        print(f"{i}. {status}")
        print(f"   Bio: {profile['biography'][:60]}...")
        print(f"   Name: {profile['full_name']}")
        print()
    
    return True

def test_industry_classification():
    """Test industry classification"""
    print("🏭 Testing Industry Classification")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    test_profiles = [
        {
            'biography': 'Professional keynote speaker helping businesses grow through leadership',
            'business_category_name': 'Public Speaker'
        },
        {
            'biography': 'Business coach and consultant | Transform your mindset | Scale your business',
            'business_category_name': 'Business Consultant'
        },
        {
            'biography': 'Bestselling author of 5 books | Writing coach | Help authors publish',
            'business_category_name': 'Author'
        },
        {
            'biography': 'Online course creator teaching digital marketing to 10k+ students',
            'business_category_name': 'Education'
        },
        {
            'biography': 'Life coach specializing in career transitions and personal growth',
            'business_category_name': 'Life Coach'
        },
        {
            'biography': 'Fitness trainer and nutrition expert helping clients get in shape',
            'business_category_name': 'Fitness'
        }
    ]
    
    for i, profile in enumerate(test_profiles, 1):
        industry = generator.classify_industry(profile)
        if industry:
            industry_name = industry.replace('_', ' ').title()
            print(f"{i}. ✅ {industry_name}")
        else:
            print(f"{i}. ❌ No target industry match")
        
        print(f"   Bio: {profile['biography'][:60]}...")
        print(f"   Category: {profile.get('business_category_name', 'None')}")
        print()
    
    return True

def test_contact_extraction():
    """Test contact information extraction"""
    print("📧 Testing Contact Information Extraction")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    test_profiles = [
        {
            'biography': 'Business coach | Contact: <EMAIL> | (555) 123-4567',
            'external_url': 'https://sarahcoaching.com'
        },
        {
            'biography': 'Keynote speaker 🎤 Email me: <EMAIL> for bookings',
            'external_url': 'https://mikespeaker.com'
        },
        {
            'biography': 'Author & writing coach | DM for collaborations | No email listed',
            'external_url': 'https://authorsite.com'
        },
        {
            'biography': 'Life coach | Call me at ******-987-6543 | Transform your life',
            'external_url': ''
        },
        {
            'biography': 'Course creator | <EMAIL> | Building online education',
            'external_url': 'https://mycourses.org'
        }
    ]
    
    for i, profile in enumerate(test_profiles, 1):
        contact = generator.extract_contact_info(profile)
        
        print(f"{i}. Contact Information:")
        print(f"   Email: {contact['email'] or '❌ None'}")
        print(f"   Phone: {contact['phone'] or '❌ None'}")
        print(f"   Website: {contact['website'] or '❌ None'}")
        print(f"   Bio: {profile['biography'][:50]}...")
        print()
    
    return True

def test_qualification_scoring():
    """Test qualification scoring system"""
    print("📊 Testing Qualification Scoring")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    # Sample qualified profiles
    test_profiles = [
        {
            'username': 'speaker_pro',
            'followers': 2500,
            'posts_count': 150,
            'is_business_account': True,
            'is_verified': False,
            'days_since_last_post': 5,
            'contact_info': {'email': '<EMAIL>', 'website': 'https://speakerpro.com', 'phone': None}
        },
        {
            'username': 'coach_expert',
            'followers': 8500,
            'posts_count': 75,
            'is_business_account': True,
            'is_verified': True,
            'days_since_last_post': 2,
            'contact_info': {'email': '<EMAIL>', 'website': 'https://coachexpert.com', 'phone': '************'}
        },
        {
            'username': 'author_jane',
            'followers': 1200,
            'posts_count': 45,
            'is_business_account': False,
            'is_verified': False,
            'days_since_last_post': 10,
            'contact_info': {'email': None, 'website': 'https://authorjane.com', 'phone': None}
        },
        {
            'username': 'new_coach',
            'followers': 350,
            'posts_count': 25,
            'is_business_account': False,
            'is_verified': False,
            'days_since_last_post': 20,
            'contact_info': {'email': '<EMAIL>', 'website': None, 'phone': None}
        }
    ]
    
    for i, profile in enumerate(test_profiles, 1):
        score = generator.calculate_qualification_score(profile)
        
        print(f"{i}. @{profile['username']} - Score: {score}/100")
        print(f"   Followers: {profile['followers']:,}")
        print(f"   Posts: {profile['posts_count']}")
        print(f"   Business: {profile['is_business_account']}")
        print(f"   Verified: {profile['is_verified']}")
        print(f"   Last post: {profile['days_since_last_post']} days ago")
        
        contact = profile['contact_info']
        contacts = []
        if contact['email']: contacts.append('Email')
        if contact['website']: contacts.append('Website')
        if contact['phone']: contacts.append('Phone')
        print(f"   Contact: {', '.join(contacts) if contacts else 'None'}")
        
        # Score breakdown
        if score >= 80:
            print(f"   🌟 EXCELLENT lead - High priority for outreach")
        elif score >= 60:
            print(f"   ✅ GOOD lead - Worth contacting")
        elif score >= 40:
            print(f"   ⚠️  FAIR lead - Consider if niche match")
        else:
            print(f"   ❌ POOR lead - Skip for now")
        print()
    
    return True

def test_google_search_simulation():
    """Test Google search URL extraction"""
    print("🔍 Testing Google Search URL Extraction")
    print("=" * 50)
    
    generator = TargetedLeadGenerator()
    
    # Simulate Google search results for each industry
    sample_results = {
        'keynote_speaker': [
            'https://www.instagram.com/keynote_speaker_pro/',
            'https://www.instagram.com/motivational_mike/',
            'https://www.instagram.com/speaker_sarah_jones/',
            'https://www.instagram.com/p/ABC123/',  # Post - filtered
            'https://www.instagram.com/professional_speaker/',
        ],
        'coach_consultant_creator': [
            'https://www.instagram.com/business_coach_jane/',
            'https://www.instagram.com/life_coach_expert/',
            'https://www.instagram.com/course_creator_pro/',
            'https://www.instagram.com/reel/XYZ789/',  # Reel - filtered
            'https://www.instagram.com/consultant_mike/',
        ],
        'author': [
            'https://www.instagram.com/bestselling_author/',
            'https://www.instagram.com/writer_jane_smith/',
            'https://www.instagram.com/book_author_pro/',
            'https://www.instagram.com/stories/highlights/123/',  # Story - filtered
            'https://www.instagram.com/published_author/',
        ]
    }
    
    for industry, urls in sample_results.items():
        print(f"\n🎯 {industry.replace('_', ' ').title()} Search Results:")
        
        valid_usernames = []
        for url in urls:
            username = generator.extract_username_from_url(url)
            if username:
                print(f"   ✅ {url} → @{username}")
                valid_usernames.append(username)
            else:
                print(f"   ❌ {url} → Filtered out")
        
        print(f"   📊 Extracted {len(valid_usernames)} valid usernames")
    
    return True

def main():
    """Run all tests"""
    print("🧪 TARGETED LEAD GENERATION - SYSTEM TESTS")
    print("=" * 60)
    print("Testing all qualification criteria and lead scoring")
    print()
    
    tests = [
        test_us_location_detection,
        test_industry_classification,
        test_contact_extraction,
        test_qualification_scoring,
        test_google_search_simulation
    ]
    
    passed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test failed with error: {e}\n")
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Your targeted lead generation system is ready!")
        print("\n🚀 Ready to run:")
        print("   python run_targeted_leads.py --test-mode")
        print("   python run_targeted_leads.py --industries keynote_speaker --max-leads 5")
        print("   python run_targeted_leads.py --industries all --max-leads 10")
        
        print("\n🎯 System will find leads matching:")
        print("   • 100-10,000 followers (engagement sweet spot)")
        print("   • US-based profiles (extensive location detection)")
        print("   • Recent posting activity (within 30 days)")
        print("   • Target industries: Speakers, Coaches, Authors")
        print("   • Qualification scoring (0-100 points)")
        print("   • Contact information extraction")
        print("   • Excel export with separate sheets per industry")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
