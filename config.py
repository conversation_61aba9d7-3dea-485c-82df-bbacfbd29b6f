import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Instagram API Configuration
INSTAGRAM_ACCESS_TOKEN = os.getenv('INSTAGRAM_ACCESS_TOKEN')
API_VERSION = 'v23.0'
BASE_URL = f'https://graph.instagram.com/{API_VERSION}'

# Search Criteria Configuration
FOLLOWER_RANGE = {
    'min': 100,
    'max': 10000
}

ACTIVITY_THRESHOLD_DAYS = 30

# Industry categories with their keywords
INDUSTRIES = {
    'speaker': [
        'speaker', 'keynote', 'public speaking', 'motivational speaker', 'presenter'
    ],
    'coach': [
        'coach', 'consultant', 'mentor', 'online coach', 'life coach', 'business coach'
    ],
    'course_creator': [
        'course', 'training', 'workshop', 'masterclass', 'program', 'online course'
    ],
    'author': [
        'author', 'writer', 'book', 'published', 'author', 'bestselling'
    ]
}

US_LOCATIONS = [
    'united states', 'usa', 'u.s.', 'u.s.a.', 'america'
]

# Output fields configuration
REQUIRED_FIELDS = [
    'first_name',
    'last_name',
    'industry',
    'follower_count',
    'post_count',
    'email',
    'username',
    'biography',
    'website'
] 