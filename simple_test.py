#!/usr/bin/env python3
"""
Simple test of core functionality without external dependencies
"""

import re
import json
from datetime import datetime

def test_username_generation():
    """Test username generation without external dependencies"""
    print("🎯 Testing Username Generation")
    print("-" * 40)
    
    def generate_usernames(base_name, industry=None):
        """Generate username variations"""
        variations = []
        base_clean = re.sub(r'[^a-zA-Z0-9]', '', base_name.lower())
        
        # Basic variations
        variations.extend([
            base_clean,
            f"{base_clean}_official",
            f"the{base_clean}",
            f"{base_clean}co",
            f"{base_clean}hq"
        ])
        
        # Industry-specific variations
        if industry:
            industry_suffixes = {
                'speaker': ['speaks', 'talks', 'keynote'],
                'coach': ['coaching', 'coach', 'mentor'],
                'author': ['writes', 'author', 'books'],
                'business': ['biz', 'company', 'ventures']
            }
            
            if industry in industry_suffixes:
                for suffix in industry_suffixes[industry]:
                    variations.extend([
                        f"{base_clean}{suffix}",
                        f"{base_clean}_{suffix}"
                    ])
        
        return list(set(variations))[:10]
    
    # Test cases
    test_cases = [
        ("<PERSON>", "speaker"),
        ("Marketing Pro", "business"),
        ("Jane Author", "author"),
        ("Fitness Coach", "coach")
    ]
    
    for name, industry in test_cases:
        variations = generate_usernames(name, industry)
        print(f"\nBase: '{name}' | Industry: {industry}")
        for i, variation in enumerate(variations[:5], 1):
            print(f"  {i}. @{variation}")
    
    return True

def test_email_extraction():
    """Test email extraction"""
    print("\n📧 Testing Email Extraction")
    print("-" * 40)
    
    def extract_email(bio):
        """Extract email from bio"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        match = re.search(email_pattern, bio)
        return match.group(0) if match else None
    
    test_bios = [
        "Keynote speaker | Contact: <EMAIL> | Available worldwide",
        "Business coach 📈 Email: <EMAIL> for inquiries",
        "Author of bestselling books 📚 No email here",
        "Consultant | <EMAIL> | 10+ years experience",
        "Fitness trainer 💪 reach <NAME_EMAIL>"
    ]
    
    for bio in test_bios:
        email = extract_email(bio)
        print(f"\nBio: '{bio[:50]}...'")
        print(f"Email: {email if email else '❌ None found'}")
    
    return True

def test_url_processing():
    """Test URL processing"""
    print("\n🔗 Testing URL Processing")
    print("-" * 40)
    
    def extract_username_from_url(url):
        """Extract username from Instagram URL"""
        pattern = r'https?://(?:www\.)?instagram\.com/([^/?#&]+)/?'
        match = re.match(pattern, url)
        
        if match:
            username = match.group(1)
            # Filter out non-username paths
            invalid_paths = {'p', 'reel', 'tv', 'stories', 'explore'}
            if username not in invalid_paths and not username.startswith('p/'):
                return username
        return None
    
    test_urls = [
        "https://www.instagram.com/johnsmith/",
        "https://instagram.com/business_coach_pro",
        "https://www.instagram.com/p/ABC123DEF/",  # Should be filtered
        "https://www.instagram.com/jane.author/",
        "https://www.instagram.com/reel/XYZ789/",   # Should be filtered
        "https://www.instagram.com/marketing_expert_2024/",
        "https://www.instagram.com/stories/highlights/123/"  # Should be filtered
    ]
    
    for url in test_urls:
        username = extract_username_from_url(url)
        status = "✅ Valid" if username else "❌ Filtered"
        print(f"{status}: {url}")
        if username:
            print(f"         → @{username}")
    
    return True

def test_profile_filtering():
    """Test profile filtering logic"""
    print("\n🔍 Testing Profile Filtering")
    print("-" * 40)
    
    # Sample profile data
    sample_profiles = [
        {
            'username': 'keynote_speaker_pro',
            'followers': 5000,
            'posts_count': 150,
            'is_private': False,
            'biography': 'Professional keynote speaker helping businesses grow',
            'is_business_account': True
        },
        {
            'username': 'private_coach',
            'followers': 2000,
            'posts_count': 50,
            'is_private': True,
            'biography': 'Life coach and mentor',
            'is_business_account': False
        },
        {
            'username': 'mega_influencer',
            'followers': 500000,
            'posts_count': 1000,
            'is_private': False,
            'biography': 'Lifestyle influencer and brand ambassador',
            'is_business_account': True
        },
        {
            'username': 'small_author',
            'followers': 50,
            'posts_count': 5,
            'is_private': False,
            'biography': 'Aspiring author writing my first book',
            'is_business_account': False
        }
    ]
    
    # Filtering criteria
    criteria = {
        'min_followers': 1000,
        'max_followers': 50000,
        'exclude_private': True,
        'min_posts': 10
    }
    
    print("Filtering criteria:")
    print(f"  • Min followers: {criteria['min_followers']:,}")
    print(f"  • Max followers: {criteria['max_followers']:,}")
    print(f"  • Exclude private: {criteria['exclude_private']}")
    print(f"  • Min posts: {criteria['min_posts']}")
    
    print("\nProfile filtering results:")
    
    for profile in sample_profiles:
        print(f"\n📋 @{profile['username']}")
        print(f"   Followers: {profile['followers']:,}")
        print(f"   Posts: {profile['posts_count']}")
        print(f"   Private: {profile['is_private']}")
        print(f"   Bio: {profile['biography'][:50]}...")
        
        # Check against criteria
        passes = True
        reasons = []
        
        if profile['followers'] < criteria['min_followers']:
            passes = False
            reasons.append("Too few followers")
        
        if profile['followers'] > criteria['max_followers']:
            passes = False
            reasons.append("Too many followers")
        
        if criteria['exclude_private'] and profile['is_private']:
            passes = False
            reasons.append("Private account")
        
        if profile['posts_count'] < criteria['min_posts']:
            passes = False
            reasons.append("Too few posts")
        
        if passes:
            print("   ✅ PASSES all criteria")
        else:
            print(f"   ❌ FILTERED: {', '.join(reasons)}")
    
    return True

def test_search_configuration():
    """Test search configuration"""
    print("\n⚙️ Testing Search Configuration")
    print("-" * 40)
    
    search_configs = {
        'speaker': {
            'keywords': ['keynote speaker', 'motivational speaker', 'public speaker'],
            'min_followers': 500,
            'max_followers': 50000,
            'target_industries': ['speaker']
        },
        'coach': {
            'keywords': ['business coach', 'life coach', 'executive coach'],
            'min_followers': 200,
            'max_followers': 25000,
            'target_industries': ['coach']
        },
        'author': {
            'keywords': ['author', 'writer', 'published author'],
            'min_followers': 100,
            'max_followers': 100000,
            'target_industries': ['author']
        }
    }
    
    for industry, config in search_configs.items():
        print(f"\n🎯 {industry.upper()} Configuration:")
        print(f"   Keywords: {', '.join(config['keywords'])}")
        print(f"   Followers: {config['min_followers']:,} - {config['max_followers']:,}")
        print(f"   Industries: {', '.join(config['target_industries'])}")
    
    return True

def create_sample_output():
    """Create sample output files"""
    print("\n📄 Creating Sample Output")
    print("-" * 40)
    
    # Sample data
    sample_results = [
        {
            'username': 'keynote_speaker_pro',
            'full_name': 'John Speaker',
            'industry': 'speaker',
            'followers': 5000,
            'posts': 150,
            'email': '<EMAIL>',
            'biography': 'Professional keynote speaker helping businesses grow',
            'timestamp': datetime.now().isoformat()
        },
        {
            'username': 'business_coach_jane',
            'full_name': 'Jane Coach',
            'industry': 'coach',
            'followers': 3200,
            'posts': 89,
            'email': '<EMAIL>',
            'biography': 'Executive coach transforming leadership teams',
            'timestamp': datetime.now().isoformat()
        }
    ]
    
    # Save as JSON
    json_filename = f"sample_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(sample_results, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created sample JSON: {json_filename}")
    
    # Show what would be in Excel
    print("\n📊 Sample Excel Output Structure:")
    print("Columns: Username | Full Name | Industry | Followers | Posts | Email | Biography")
    for result in sample_results:
        print(f"Row: @{result['username']} | {result['full_name']} | {result['industry']} | {result['followers']:,} | {result['posts']} | {result['email']} | {result['biography'][:30]}...")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Instagram Username Search Tool - Core Functionality Test")
    print("=" * 60)
    
    tests = [
        test_username_generation,
        test_email_extraction,
        test_url_processing,
        test_profile_filtering,
        test_search_configuration,
        create_sample_output
    ]
    
    success_count = 0
    
    for test in tests:
        try:
            if test():
                success_count += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
    
    print(f"\n🎉 Core Functionality Test Complete!")
    print(f"📊 {success_count}/{len(tests)} tests passed")
    
    if success_count == len(tests):
        print("\n✅ All core features working correctly!")
        print("\n🚀 Ready to install dependencies and run full searches:")
        print("   1. pip install googlesearch-python instaloader openpyxl scikit-learn")
        print("   2. python run_username_search.py --help")
        print("   3. python run_username_search.py --mode search --keywords 'keynote speaker'")
    else:
        print("\n⚠️  Some core features need attention.")

if __name__ == "__main__":
    main()
