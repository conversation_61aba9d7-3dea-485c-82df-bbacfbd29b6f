from instagram_searcher import Instagram<PERSON>ccountSearcher
import json
import csv
import pandas as pd
from datetime import datetime
import logging
from config import RE<PERSON><PERSON>RED_FIELDS

def save_to_csv(results: list, filename: str):
    """
    Save results to CSV file
    """
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=REQUIRED_FIELDS)
        writer.writeheader()
        writer.writerows(results)

def save_to_excel(results: list, filename: str):
    """
    Save results to Excel file
    """
    df = pd.DataFrame(results)
    df.to_excel(filename, index=False)

def main():
    # Initialize searcher
    searcher = InstagramAccountSearcher()
    logger = logging.getLogger(__name__)
    
    # Search terms for different industries
    search_terms = [
        "keynote speaker",
        "online coach",
        "consultant",
        "course creator",
        "author",
        "public speaker"
    ]
    
    all_results = []
    
    # Search for each term
    for term in search_terms:
        logger.info(f"Searching for: {term}")
        accounts = searcher.search_accounts(term)
        filtered_accounts = searcher.filter_accounts(accounts)
        all_results.extend(filtered_accounts)
        logger.info(f"Found {len(filtered_accounts)} matching accounts for term: {term}")
    
    # Remove duplicates
    unique_results = {account['username']: account for account in all_results}.values()
    
    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save results to JSON
    json_filename = f"instagram_search_results_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(list(unique_results), f, indent=2, ensure_ascii=False)
    
    # Save results to CSV
    csv_filename = f"instagram_search_results_{timestamp}.csv"
    save_to_csv(list(unique_results), csv_filename)
    
    # Save results to Excel
    excel_filename = f"instagram_search_results_{timestamp}.xlsx"
    save_to_excel(list(unique_results), excel_filename)
    
    logger.info(f"Found {len(unique_results)} unique matching accounts")
    logger.info(f"Results saved to:")
    logger.info(f"- JSON: {json_filename}")
    logger.info(f"- CSV: {csv_filename}")
    logger.info(f"- Excel: {excel_filename}")

if __name__ == "__main__":
    main() 