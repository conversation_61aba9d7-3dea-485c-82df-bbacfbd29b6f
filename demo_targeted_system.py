#!/usr/bin/env python3
"""
Demo of Targeted Instagram Lead Generation System
Shows how the qualification criteria work
"""

import re

def demo_us_location_detection():
    """Demo US location detection"""
    print("🇺🇸 US LOCATION DETECTION")
    print("=" * 40)
    
    # US location indicators
    us_indicators = [
        'california', 'texas', 'new york', 'florida', 'chicago', 'los angeles',
        'usa', 'united states', 'america', 'us', 'u.s.', 'american',
        'nationwide', 'coast to coast', 'based in us', 'ca', 'tx', 'ny', 'fl'
    ]
    
    def is_us_based(bio, url, name):
        text = f"{bio} {url} {name}".lower()
        return any(indicator in text for indicator in us_indicators)
    
    test_profiles = [
        ("Business coach based in California helping entrepreneurs", "mycoaching.com", "<PERSON> Johnson"),
        ("Keynote speaker | Available nationwide | Book me", "speaker.com", "Mike Speaker"),
        ("Author living in NYC | Published 3 books", "author.com", "Jane Author"),
        ("Life coach from London helping people transform", "uk-coaching.co.uk", "British Coach"),
        ("Consultant | Texas-based | Helping startups", "consultant.com", "Tom Texas")
    ]
    
    for i, (bio, url, name) in enumerate(test_profiles, 1):
        is_us = is_us_based(bio, url, name)
        status = "✅ US-based" if is_us else "❌ Not US-based"
        print(f"{i}. {status}")
        print(f"   Bio: {bio[:50]}...")
        print(f"   Name: {name}")
        print()

def demo_industry_classification():
    """Demo industry classification"""
    print("🏭 INDUSTRY CLASSIFICATION")
    print("=" * 40)
    
    industry_keywords = {
        'keynote_speaker': ['keynote speaker', 'public speaker', 'motivational speaker', 'conference speaker'],
        'coach_consultant_creator': ['business coach', 'life coach', 'consultant', 'course creator', 'online coach'],
        'author': ['author', 'writer', 'bestselling author', 'published author', 'book author']
    }
    
    def classify_industry(bio, category):
        text = f"{bio} {category}".lower()
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in text for keyword in keywords):
                return industry
        return None
    
    test_profiles = [
        ("Professional keynote speaker helping businesses grow", "Public Speaker"),
        ("Business coach and consultant | Transform your mindset", "Business Consultant"),
        ("Bestselling author of 5 books | Writing coach", "Author"),
        ("Online course creator teaching digital marketing", "Education"),
        ("Life coach specializing in career transitions", "Life Coach"),
        ("Fitness trainer and nutrition expert", "Fitness")
    ]
    
    for i, (bio, category) in enumerate(test_profiles, 1):
        industry = classify_industry(bio, category)
        if industry:
            industry_name = industry.replace('_', ' ').title()
            print(f"{i}. ✅ {industry_name}")
        else:
            print(f"{i}. ❌ No target industry match")
        
        print(f"   Bio: {bio[:50]}...")
        print(f"   Category: {category}")
        print()

def demo_contact_extraction():
    """Demo contact extraction"""
    print("📧 CONTACT EXTRACTION")
    print("=" * 40)
    
    def extract_contact(bio, url):
        contact = {'email': None, 'phone': None, 'website': url if url else None}
        
        # Extract email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, bio)
        if email_match:
            contact['email'] = email_match.group(0)
        
        # Extract phone
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',
            r'\+1[-.\s]?\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        ]
        
        for pattern in phone_patterns:
            phone_match = re.search(pattern, bio)
            if phone_match:
                contact['phone'] = phone_match.group(0)
                break
        
        return contact
    
    test_profiles = [
        ("Business coach | Contact: <EMAIL> | (*************", "sarahcoaching.com"),
        ("Keynote speaker 🎤 Email me: <EMAIL> for bookings", "mikespeaker.com"),
        ("Author & writing coach | DM for collaborations", "authorsite.com"),
        ("Life coach | Call me at ******-987-6543", ""),
        ("Course creator | <EMAIL>", "mycourses.org")
    ]
    
    for i, (bio, url) in enumerate(test_profiles, 1):
        contact = extract_contact(bio, url)
        
        print(f"{i}. Contact Information:")
        print(f"   Email: {contact['email'] or '❌ None'}")
        print(f"   Phone: {contact['phone'] or '❌ None'}")
        print(f"   Website: {contact['website'] or '❌ None'}")
        print(f"   Bio: {bio[:40]}...")
        print()

def demo_qualification_scoring():
    """Demo qualification scoring"""
    print("📊 QUALIFICATION SCORING")
    print("=" * 40)
    
    def calculate_score(profile):
        score = 0
        
        # Follower count (0-25 points)
        followers = profile['followers']
        if 500 <= followers <= 5000:
            score += 25  # Sweet spot
        elif 100 <= followers <= 10000:
            score += 20  # Good range
        
        # Account type (0-20 points)
        if profile.get('is_business_account'):
            score += 10
        if profile.get('is_verified'):
            score += 10
        
        # Content activity (0-20 points)
        posts = profile.get('posts_count', 0)
        if posts >= 100:
            score += 20
        elif posts >= 50:
            score += 15
        elif posts >= 20:
            score += 10
        
        # Contact info (0-20 points)
        contact = profile.get('contact_info', {})
        if contact.get('email'):
            score += 10
        if contact.get('website'):
            score += 5
        if contact.get('phone'):
            score += 5
        
        # Recent activity (0-15 points)
        days_since_post = profile.get('days_since_last_post')
        if days_since_post is not None:
            if days_since_post <= 7:
                score += 15
            elif days_since_post <= 14:
                score += 10
            elif days_since_post <= 30:
                score += 5
        
        return min(score, 100)
    
    test_profiles = [
        {
            'username': 'speaker_pro',
            'followers': 2500,
            'posts_count': 150,
            'is_business_account': True,
            'is_verified': False,
            'days_since_last_post': 5,
            'contact_info': {'email': '<EMAIL>', 'website': 'speakerpro.com', 'phone': None}
        },
        {
            'username': 'coach_expert',
            'followers': 8500,
            'posts_count': 75,
            'is_business_account': True,
            'is_verified': True,
            'days_since_last_post': 2,
            'contact_info': {'email': '<EMAIL>', 'website': 'coachexpert.com', 'phone': '************'}
        },
        {
            'username': 'author_jane',
            'followers': 1200,
            'posts_count': 45,
            'is_business_account': False,
            'is_verified': False,
            'days_since_last_post': 10,
            'contact_info': {'email': None, 'website': 'authorjane.com', 'phone': None}
        }
    ]
    
    for i, profile in enumerate(test_profiles, 1):
        score = calculate_score(profile)
        
        print(f"{i}. @{profile['username']} - Score: {score}/100")
        print(f"   Followers: {profile['followers']:,}")
        print(f"   Posts: {profile['posts_count']}")
        print(f"   Business: {profile['is_business_account']}")
        print(f"   Last post: {profile['days_since_last_post']} days ago")
        
        if score >= 80:
            print(f"   🌟 EXCELLENT lead - High priority")
        elif score >= 60:
            print(f"   ✅ GOOD lead - Worth contacting")
        elif score >= 40:
            print(f"   ⚠️  FAIR lead - Consider if niche match")
        else:
            print(f"   ❌ POOR lead - Skip")
        print()

def demo_search_criteria():
    """Demo search criteria"""
    print("🎯 SEARCH CRITERIA SUMMARY")
    print("=" * 40)
    
    criteria = {
        'Follower Range': '100 - 10,000 (engagement sweet spot)',
        'Location': 'US-based (extensive detection)',
        'Account Type': 'Public profiles only',
        'Activity': 'Posted within 30 days',
        'Content': 'Minimum 5 posts',
        'Industries': 'Speakers, Coaches, Authors'
    }
    
    for criterion, description in criteria.items():
        print(f"✅ {criterion}: {description}")
    
    print(f"\n🎯 Target Industries:")
    industries = [
        "Keynote Speakers",
        "Online Coaches/Consultants/Course Creators", 
        "Authors"
    ]
    
    for industry in industries:
        print(f"   • {industry}")
    
    print(f"\n📊 Qualification Scoring (0-100 points):")
    scoring = [
        "Follower count in sweet spot (25 pts)",
        "Business/verified account (20 pts)",
        "High content activity (20 pts)",
        "Contact information available (20 pts)",
        "Recent posting activity (15 pts)"
    ]
    
    for score_item in scoring:
        print(f"   • {score_item}")

def main():
    """Run the demo"""
    print("🚀 TARGETED INSTAGRAM LEAD GENERATION SYSTEM")
    print("=" * 60)
    print("Demonstration of qualification criteria and lead scoring")
    print()
    
    demos = [
        demo_us_location_detection,
        demo_industry_classification,
        demo_contact_extraction,
        demo_qualification_scoring,
        demo_search_criteria
    ]
    
    for demo in demos:
        demo()
        print()
    
    print("=" * 60)
    print("🎉 SYSTEM DEMONSTRATION COMPLETE!")
    print("=" * 60)
    
    print("\n✅ Your targeted lead generation system includes:")
    print("• Extensive US location detection (50+ indicators)")
    print("• Industry classification for 3 target markets")
    print("• Contact information extraction (email, phone, website)")
    print("• Qualification scoring (0-100 points)")
    print("• Google search integration")
    print("• Instagram profile verification")
    print("• Excel export with separate sheets per industry")
    
    print("\n🚀 Ready to generate real leads:")
    print("1. python run_targeted_leads.py --test-mode")
    print("2. python run_targeted_leads.py --industries keynote_speaker --max-leads 5")
    print("3. python run_targeted_leads.py --industries all --max-leads 10")
    
    print("\n💡 Expected results:")
    print("• High-quality leads with 100-10k followers")
    print("• US-based professionals in target industries")
    print("• Active accounts with recent posts")
    print("• Contact information for direct outreach")
    print("• Qualification scores to prioritize outreach")

if __name__ == "__main__":
    main()
