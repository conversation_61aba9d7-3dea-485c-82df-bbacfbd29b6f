#!/usr/bin/env python3
"""
Simple username search demonstration
"""

import sys
import time
import random
from datetime import datetime

def demo_username_availability():
    """Demo username availability checking"""
    print("🔍 Username Availability Check Demo")
    print("=" * 50)
    
    try:
        import instaloader
        
        # Initialize instaloader
        L = instaloader.Instaloader(
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Test usernames to check
        test_usernames = [
            "johnsmith12345test",
            "businesscoach99999",
            "speakertest12345",
            "authortest99999",
            "coachtest12345"
        ]
        
        print("Testing username availability...")
        available_usernames = []
        
        for i, username in enumerate(test_usernames, 1):
            print(f"\n[{i}/{len(test_usernames)}] Checking @{username}...")
            
            try:
                # Add small delay to be respectful
                time.sleep(random.uniform(1, 3))
                
                # Try to get profile
                instaloader.Profile.from_username(L.context, username)
                print(f"   ❌ Taken: @{username}")
                
            except instaloader.exceptions.ProfileNotExistsException:
                print(f"   ✅ Available: @{username}")
                available_usernames.append(username)
                
            except Exception as e:
                print(f"   ⚠️  Error checking @{username}: {e}")
        
        print(f"\n📊 Results:")
        print(f"   Available usernames: {len(available_usernames)}")
        print(f"   Taken usernames: {len(test_usernames) - len(available_usernames)}")
        
        if available_usernames:
            print(f"\n✅ Available usernames found:")
            for username in available_usernames:
                print(f"   • @{username}")
        
        return True
        
    except ImportError:
        print("❌ instaloader not installed. Run: pip install instaloader")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_google_search_simulation():
    """Demo Google search for Instagram profiles"""
    print("\n🔍 Google Search Simulation")
    print("=" * 50)
    
    # Simulate what Google search would return
    sample_search_results = [
        "https://www.instagram.com/businesscoach_sarah/",
        "https://www.instagram.com/keynote_speaker_mike/",
        "https://www.instagram.com/author_jane_smith/",
        "https://www.instagram.com/life_coach_pro/",
        "https://www.instagram.com/p/ABC123/",  # Post - should be filtered
        "https://www.instagram.com/consultant_expert/",
        "https://www.instagram.com/reel/XYZ789/",  # Reel - should be filtered
        "https://www.instagram.com/speaker_academy/"
    ]
    
    print("Sample Google search results for 'site:instagram.com business coach':")
    
    import re
    
    def extract_username_from_url(url):
        """Extract username from Instagram URL"""
        pattern = r'https?://(?:www\.)?instagram\.com/([^/?#&]+)/?'
        match = re.match(pattern, url)
        
        if match:
            username = match.group(1)
            # Filter out non-username paths
            invalid_paths = {'p', 'reel', 'tv', 'stories', 'explore'}
            if username not in invalid_paths and not username.startswith('p/'):
                return username
        return None
    
    valid_usernames = []
    
    for url in sample_search_results:
        username = extract_username_from_url(url)
        if username:
            print(f"✅ {url} → @{username}")
            valid_usernames.append(username)
        else:
            print(f"❌ {url} → Filtered out")
    
    print(f"\n📊 Extracted {len(valid_usernames)} valid usernames:")
    for username in valid_usernames:
        print(f"   • @{username}")
    
    return valid_usernames

def demo_profile_verification():
    """Demo profile verification with real Instagram accounts"""
    print("\n🔍 Profile Verification Demo")
    print("=" * 50)
    
    try:
        import instaloader
        
        # Initialize instaloader
        L = instaloader.Instaloader(
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Test with some well-known accounts
        test_accounts = ["instagram", "businesscoach"]  # Keep it simple
        
        print("Testing profile verification...")
        
        for i, username in enumerate(test_accounts, 1):
            print(f"\n[{i}/{len(test_accounts)}] Verifying @{username}...")
            
            try:
                # Add delay to be respectful
                time.sleep(random.uniform(2, 4))
                
                profile = instaloader.Profile.from_username(L.context, username)
                
                print(f"   ✅ Found: @{username}")
                print(f"      Name: {profile.full_name}")
                print(f"      Followers: {profile.followers:,}")
                print(f"      Posts: {profile.mediacount}")
                print(f"      Private: {profile.is_private}")
                print(f"      Verified: {profile.is_verified}")
                print(f"      Bio: {profile.biography[:50]}...")
                
            except instaloader.exceptions.ProfileNotExistsException:
                print(f"   ❌ Not found: @{username}")
                
            except Exception as e:
                print(f"   ⚠️  Error: {e}")
                break  # Stop on error to avoid rate limiting
        
        return True
        
    except ImportError:
        print("❌ instaloader not installed")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run the simple search demo"""
    print("🚀 Instagram Username Search - Simple Demo")
    print("=" * 60)
    print("This demo shows the core functionality without making too many API calls")
    print()
    
    # Demo 1: Google search simulation
    usernames = demo_google_search_simulation()
    
    # Demo 2: Username availability (with test usernames)
    demo_username_availability()
    
    # Demo 3: Profile verification (limited to avoid rate limits)
    demo_profile_verification()
    
    print("\n" + "=" * 60)
    print("🎉 Simple Demo Complete!")
    print("=" * 60)
    
    print("\n✅ What we demonstrated:")
    print("• Google search URL extraction")
    print("• Username availability checking")
    print("• Real Instagram profile verification")
    print("• Profile data extraction")
    
    print("\n🚀 Ready for full searches:")
    print("• Use Google search to find real usernames")
    print("• Verify profiles with instaloader")
    print("• Filter by criteria (followers, industry, etc.)")
    print("• Export to Excel for lead generation")
    
    print("\n💡 Next steps:")
    print("1. Manually search Google for: site:instagram.com 'business coach'")
    print("2. Extract usernames from the results")
    print("3. Use this tool to verify and filter them")
    print("4. Export qualified leads to Excel")

if __name__ == "__main__":
    main()
