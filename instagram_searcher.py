import requests
from datetime import datetime, timedelta
import json
import logging
import re
from typing import List, Dict, Optional, <PERSON><PERSON>
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('instagram_search.log'),
        logging.StreamHandler()
    ]
)

class InstagramAccountSearcher:
    def __init__(self):
        self.access_token = INSTAGRAM_ACCESS_TOKEN
        if not self.access_token:
            raise ValueError("Instagram access token not found. Please check your .env file.")
            
        self.base_url = BASE_URL
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        self.logger = logging.getLogger(__name__)

    def extract_name_from_bio(self, bio: str) -> Tuple[str, str]:
        """
        Extract first and last name from biography
        """
        # Common patterns for names in bios
        name_patterns = [
            r'([A-Z][a-z]+)\s+([A-Z][a-z]+)',  # First Last
            r'([A-Z][a-z]+)\s+([A-Z][a-z]+)\s+[|]',  # First Last |
            r'([A-Z][a-z]+)\s+([A-Z][a-z]+)\s+[•]',  # First Last •
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, bio)
            if match:
                return match.group(1), match.group(2)
        
        return "Unknown", "Unknown"

    def extract_email(self, bio: str, website: str) -> str:
        """
        Extract email from biography or website
        """
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        
        # Check bio
        bio_match = re.search(email_pattern, bio)
        if bio_match:
            return bio_match.group(0)
            
        # Check website
        website_match = re.search(email_pattern, website)
        if website_match:
            return website_match.group(0)
            
        return ""

    def search_accounts(self, query: str, limit: int = 100) -> List[Dict]:
        """
        Search for Instagram accounts based on query
        """
        # First, get the Instagram Business Account ID
        try:
            # Get the user's Instagram Business Account
            me_endpoint = f"{self.base_url}/me"
            me_params = {
                'fields': 'id,instagram_business_account',
                'access_token': self.access_token
            }
            
            me_response = requests.get(me_endpoint, params=me_params)
            me_response.raise_for_status()
            me_data = me_response.json()
            
            if 'instagram_business_account' not in me_data:
                self.logger.error("No Instagram Business Account found")
                return []
                
            instagram_account_id = me_data['instagram_business_account']['id']
            
            # Now search for hashtags related to the query
            hashtag_endpoint = f"{self.base_url}/{instagram_account_id}/tags"
            hashtag_params = {
                'q': query,
                'access_token': self.access_token
            }
            
            hashtag_response = requests.get(hashtag_endpoint, params=hashtag_params)
            hashtag_response.raise_for_status()
            hashtag_data = hashtag_response.json().get('data', [])
            
            if not hashtag_data:
                self.logger.info(f"No hashtags found for query: {query}")
                return []
                
            # Get recent media for the first hashtag
            hashtag_id = hashtag_data[0]['id']
            media_endpoint = f"{self.base_url}/{hashtag_id}/recent_media"
            media_params = {
                'fields': 'id,username,caption,media_type,timestamp',
                'limit': limit,
                'access_token': self.access_token
            }
            
            media_response = requests.get(media_endpoint, params=media_params)
            media_response.raise_for_status()
            media_data = media_response.json().get('data', [])
            
            # Extract unique usernames
            accounts = []
            seen_usernames = set()
            
            for media in media_data:
                username = media.get('username')
                if username and username not in seen_usernames:
                    seen_usernames.add(username)
                    accounts.append({
                        'id': media.get('id'),
                        'username': username
                    })
            
            return accounts
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error searching accounts: {e}")
            return []

    def get_account_details(self, account_id: str) -> Optional[Dict]:
        """
        Get detailed information about an account
        """
        try:
            endpoint = f"{self.base_url}/{account_id}"
            params = {
                'fields': 'id,username,biography,followers_count,media_count,website',
                'access_token': self.access_token
            }
            
            response = requests.get(endpoint, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error getting account details: {e}")
            return None

    def check_recent_activity(self, account_id: str) -> bool:
        """
        Check if account has recent activity
        """
        endpoint = f"{self.base_url}/{account_id}/media"
        params = {
            'fields': 'timestamp',
            'limit': 1,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(endpoint, params=params)
            response.raise_for_status()
            data = response.json().get('data', [])
            
            if not data:
                return False
                
            latest_post = datetime.fromisoformat(data[0]['timestamp'].replace('Z', '+00:00'))
            return (datetime.now() - latest_post).days <= ACTIVITY_THRESHOLD_DAYS
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error checking recent activity: {e}")
            return False

    def is_us_based(self, account_data: Dict) -> bool:
        """
        Check if account is US-based
        """
        bio = account_data.get('biography', '').lower()
        return any(location in bio for location in US_LOCATIONS)

    def classify_industry(self, account_data: Dict) -> str:
        """
        Classify account into a single primary industry
        """
        bio = account_data.get('biography', '').lower()
        website = account_data.get('website', '').lower()
        
        # Count matches for each industry
        industry_matches = {}
        for industry, keywords in INDUSTRIES.items():
            matches = sum(1 for keyword in keywords if keyword in bio or keyword in website)
            if matches > 0:
                industry_matches[industry] = matches
        
        # Return the industry with the most matches
        if industry_matches:
            return max(industry_matches.items(), key=lambda x: x[1])[0]
        return "unknown"

    def filter_accounts(self, accounts: List[Dict]) -> List[Dict]:
        """
        Filter accounts based on criteria
        """
        filtered_accounts = []
        
        for account in accounts:
            try:
                details = self.get_account_details(account['id'])
                if not details:
                    continue
                    
                # Check follower count
                followers = details.get('followers_count', 0)
                if not (FOLLOWER_RANGE['min'] <= followers <= FOLLOWER_RANGE['max']):
                    continue
                    
                # Check recent activity
                if not self.check_recent_activity(account['id']):
                    continue
                    
                # Check US location
                if not self.is_us_based(details):
                    continue
                
                # Extract name and email
                first_name, last_name = self.extract_name_from_bio(details.get('biography', ''))
                email = self.extract_email(
                    details.get('biography', ''),
                    details.get('website', '')
                )
                
                # Classify industry
                industry = self.classify_industry(details)
                if industry == "unknown":
                    continue
                
                # Add filtered account with additional information
                filtered_account = {
                    'first_name': first_name,
                    'last_name': last_name,
                    'industry': industry,
                    'follower_count': followers,
                    'post_count': details.get('media_count', 0),
                    'email': email,
                    'username': account['username'],
                    'biography': details.get('biography', ''),
                    'website': details.get('website', '')
                }
                
                filtered_accounts.append(filtered_account)
                self.logger.info(f"Found matching account: {account['username']}")
                
            except Exception as e:
                self.logger.error(f"Error processing account {account.get('username', 'unknown')}: {e}")
                continue
            
        return filtered_accounts 