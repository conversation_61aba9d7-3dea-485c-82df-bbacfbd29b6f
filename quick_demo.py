#!/usr/bin/env python3
"""
Quick demonstration of the Instagram Username Search Tool
"""

print("🚀 Instagram Username Search Tool - Quick Demo")
print("=" * 50)

# Test 1: Username Generation
print("\n1. 🎯 Username Generation")
print("-" * 30)

def generate_usernames(base_name, industry=None):
    """Generate username variations"""
    import re
    
    variations = []
    base_clean = re.sub(r'[^a-zA-Z0-9]', '', base_name.lower())
    
    # Basic variations
    variations.extend([
        base_clean,
        f"{base_clean}_official",
        f"the{base_clean}",
        f"{base_clean}co",
        f"{base_clean}hq"
    ])
    
    # Industry-specific variations
    if industry:
        industry_suffixes = {
            'speaker': ['speaks', 'talks', 'keynote'],
            'coach': ['coaching', 'coach', 'mentor'],
            'author': ['writes', 'author', 'books'],
            'business': ['biz', 'company', 'ventures']
        }
        
        if industry in industry_suffixes:
            for suffix in industry_suffixes[industry]:
                variations.extend([
                    f"{base_clean}{suffix}",
                    f"{base_clean}_{suffix}"
                ])
    
    return list(set(variations))[:10]  # Return first 10 unique

# Demo username generation
test_names = [
    ("<PERSON>", "speaker"),
    ("Marketing Pro", "business"),
    ("Jane Author", "author")
]

for name, industry in test_names:
    variations = generate_usernames(name, industry)
    print(f"\nBase: '{name}' | Industry: {industry}")
    for i, variation in enumerate(variations[:5], 1):
        print(f"  {i}. @{variation}")

# Test 2: Email Extraction
print("\n\n2. 📧 Email Extraction")
print("-" * 30)

import re

def extract_email(bio):
    """Extract email from bio"""
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    match = re.search(email_pattern, bio)
    return match.group(0) if match else None

test_bios = [
    "Keynote speaker | Contact: <EMAIL> | Available worldwide",
    "Business coach 📈 Email: <EMAIL> for inquiries",
    "Author of bestselling books 📚 No email here",
    "Consultant | <EMAIL> | 10+ years experience"
]

for bio in test_bios:
    email = extract_email(bio)
    print(f"\nBio: '{bio[:50]}...'")
    print(f"Email: {email if email else '❌ None found'}")

# Test 3: URL Processing
print("\n\n3. 🔗 URL Processing")
print("-" * 30)

def extract_username_from_url(url):
    """Extract username from Instagram URL"""
    pattern = r'https?://(?:www\.)?instagram\.com/([^/?#&]+)/?'
    match = re.match(pattern, url)
    
    if match:
        username = match.group(1)
        # Filter out non-username paths
        invalid_paths = {'p', 'reel', 'tv', 'stories', 'explore'}
        if username not in invalid_paths and not username.startswith('p/'):
            return username
    return None

test_urls = [
    "https://www.instagram.com/johnsmith/",
    "https://instagram.com/business_coach",
    "https://www.instagram.com/p/ABC123/",  # Should be filtered
    "https://www.instagram.com/jane.author/",
    "https://www.instagram.com/reel/XYZ789/"  # Should be filtered
]

for url in test_urls:
    username = extract_username_from_url(url)
    status = "✅ Valid" if username else "❌ Filtered"
    print(f"{status}: {url}")
    if username:
        print(f"         → @{username}")

# Test 4: Search Configuration
print("\n\n4. ⚙️ Search Configuration Examples")
print("-" * 30)

search_configs = {
    'speaker': {
        'keywords': ['keynote speaker', 'motivational speaker', 'public speaker'],
        'min_followers': 500,
        'max_followers': 50000
    },
    'coach': {
        'keywords': ['business coach', 'life coach', 'executive coach'],
        'min_followers': 200,
        'max_followers': 25000
    },
    'author': {
        'keywords': ['author', 'writer', 'published author'],
        'min_followers': 100,
        'max_followers': 100000
    }
}

for industry, config in search_configs.items():
    print(f"\n🎯 {industry.upper()}:")
    print(f"   Keywords: {', '.join(config['keywords'])}")
    print(f"   Followers: {config['min_followers']:,} - {config['max_followers']:,}")

print("\n\n✅ Demo Complete!")
print("\n🚀 Next Steps:")
print("1. Install dependencies: pip install -r requirements.txt")
print("2. Run full search: python run_username_search.py --help")
print("3. Check availability: python run_username_search.py --mode availability --names 'your name'")
print("4. Search profiles: python run_username_search.py --mode search --keywords 'keynote speaker'")

print("\n📚 Features Available:")
print("• Google search integration (no login required)")
print("• Instaloader profile verification")
print("• Machine learning industry classification")
print("• Username availability checking")
print("• Excel and JSON export")
print("• Proxy rotation support")
print("• Advanced filtering options")
