# Instagram Username Search Tool

A comprehensive Python tool for finding viable Instagram usernames through multiple search methods including Google search, profile verification, industry classification, and username availability checking.

## Features

### 🔍 **Multi-Method Search**
- **Google Search Integration**: Find Instagram profiles using keyword searches without requiring login
- **Instaloader Verification**: Verify profiles and extract detailed information
- **Industry Classification**: Machine learning-powered industry classification
- **Username Generation**: Generate creative username variations

### 🎯 **Advanced Filtering**
- Follower count ranges
- Account activity levels
- Industry-specific targeting
- Email/contact information extraction
- Business account detection

### 📊 **Export Options**
- Excel spreadsheets with detailed profile data
- JSON format for programmatic use
- Separate sheets by industry
- Timestamp tracking

### 🔄 **Proxy Support**
- Proxy rotation for large-scale searches
- Rate limiting and error handling
- Compliance with platform guidelines

## Installation

1. **Clone or download the files to your project directory**

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Verify installation:**
```bash
python test_username_search.py
```

## Quick Start

### Search for Existing Profiles
```bash
# Search for keynote speakers
python run_username_search.py --mode search --keywords "keynote speaker" "motivational speaker"

# Search for business coaches with specific criteria
python run_username_search.py --mode search --keywords "business coach" --num-results 100
```

### Check Username Availability
```bash
# Check availability for name variations
python run_username_search.py --mode availability --names "john smith" "jane doe"

# Check with industry context
python run_username_search.py --mode availability --names "alex johnson" --industry speaker
```

### Combined Search
```bash
# Search existing profiles AND check availability
python run_username_search.py --mode both --keywords "author" --names "writer pro"
```

## Programmatic Usage

### Basic Search Example
```python
from username_searcher import UsernameSearcher

# Initialize searcher
searcher = UsernameSearcher()

# Define search criteria
keywords = ["keynote speaker", "business coach"]
criteria = {
    'min_followers': 500,
    'max_followers': 50000,
    'exclude_private': True,
    'target_industries': ['speaker', 'coach']
}

# Search and filter profiles
profiles = searcher.search_and_verify_usernames(keywords, criteria, num_results=50)

# Export results
excel_file = searcher.export_to_excel(profiles)
print(f"Results saved to: {excel_file}")
```

### Username Availability Check
```python
from username_searcher import UsernameSearcher

searcher = UsernameSearcher()

# Check availability for variations
base_names = ["john smith", "business expert"]
available = searcher.find_available_usernames(base_names, industry="coach")

# Show available usernames
for username_data in available:
    print(f"✅ @{username_data['username']} is available!")
```

## Configuration

### Industry-Specific Searches
The tool includes pre-configured search settings for different industries:

- **Speaker**: Keynote speakers, motivational speakers, conference speakers
- **Coach**: Business coaches, life coaches, executive coaches  
- **Author**: Writers, novelists, published authors
- **Course Creator**: Online educators, training providers
- **Consultant**: Business consultants, strategy advisors

### Custom Search Criteria
```python
criteria = {
    'min_followers': 100,        # Minimum follower count
    'max_followers': 100000,     # Maximum follower count
    'exclude_private': True,     # Exclude private accounts
    'min_posts': 5,             # Minimum post count
    'target_industries': ['speaker', 'coach'],  # Target industries
    'require_email': False      # Require email in bio
}
```

## Advanced Features

### Proxy Rotation
For large-scale searches, enable proxy rotation:

```bash
# Create proxy list file (one proxy per line)
echo "http://proxy1:port" > proxies.txt
echo "http://proxy2:port" >> proxies.txt

# Run with proxy rotation
python run_username_search.py --use-proxies --proxy-file proxies.txt
```

### Machine Learning Classification
The tool uses machine learning to classify profiles by industry:

```python
searcher = UsernameSearcher()

# Classify a biography
bio = "Keynote speaker helping businesses grow through leadership"
industry = searcher.classify_industry(bio)
print(f"Classified as: {industry}")  # Output: speaker
```

### Username Generation Patterns
Generate creative username variations:

```python
searcher = UsernameSearcher()

# Generate variations with industry context
variations = searcher.generate_username_variations("john smith", industry="speaker")
# Results: johnsmith, johnsmithspeaks, thejohnsmith, johnsmith_keynote, etc.
```

## Output Formats

### Excel Export
- **Username**: Instagram handle
- **Full Name**: Display name
- **Industry**: Classified industry
- **Followers/Following**: Counts
- **Contact Info**: Email, phone (if available)
- **Account Details**: Verification status, business account, etc.

### JSON Export
Complete profile data in JSON format for programmatic processing.

## Rate Limiting & Compliance

The tool implements several compliance measures:

- **Rate Limiting**: Automatic delays between requests
- **Error Handling**: Graceful handling of API limits
- **Proxy Rotation**: Distribute requests across multiple IPs
- **Respectful Scraping**: Follows platform guidelines

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install -r requirements.txt
   ```

2. **Rate Limiting**
   - Increase delays in configuration
   - Use proxy rotation
   - Reduce batch sizes

3. **No Results Found**
   - Try broader keywords
   - Adjust filtering criteria
   - Check internet connection

### Debug Mode
Enable verbose logging:
```bash
python run_username_search.py --verbose
```

## File Structure

```
├── username_searcher.py          # Main searcher class
├── run_username_search.py        # Command-line interface
├── username_search_config.py     # Configuration settings
├── test_username_search.py       # Test suite
├── requirements.txt              # Dependencies
└── README_username_search.md     # This documentation
```

## Examples

### Find Authors with Email Contacts
```python
criteria = {
    'target_industries': ['author'],
    'require_email': True,
    'min_followers': 1000
}
profiles = searcher.search_and_verify_usernames(["author", "writer"], criteria)
```

### Check Business Name Availability
```python
business_names = ["tech solutions", "marketing pro", "consulting group"]
available = searcher.find_available_usernames(business_names, industry="business")
```

### Export by Industry
```python
# Search multiple industries
all_profiles = []
for industry in ['speaker', 'coach', 'author']:
    config = INDUSTRY_SEARCH_CONFIGS[industry]
    profiles = searcher.search_and_verify_usernames(
        config['keywords'], 
        config['criteria']
    )
    all_profiles.extend(profiles)

# Export with industry separation
searcher.export_to_excel(all_profiles, "multi_industry_search.xlsx")
```

## License & Disclaimer

This tool is for educational and research purposes. Users must comply with Instagram's Terms of Service and applicable laws. The authors are not responsible for misuse of this tool.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Run the test suite: `python test_username_search.py`
3. Enable verbose logging for detailed error information
