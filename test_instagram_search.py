from instagram_searcher import InstagramAccountSearcher
import logging
from config import INSTAGRAM_ACCESS_TOKEN
import json
import requests

def test_connection():
    """Test the Instagram API connection"""
    try:
        searcher = InstagramAccountSearcher()
        print("✅ Successfully initialized InstagramAccountSearcher")
        return searcher
    except Exception as e:
        print(f"❌ Error initializing InstagramAccountSearcher: {e}")
        return None

def test_me_endpoint():
    """Test the /me endpoint with basic fields"""
    try:
        base_url = "https://graph.instagram.com/v23.0"
        endpoint = f"{base_url}/me"
        params = {
            'fields': 'id,name',
            'access_token': INSTAGRAM_ACCESS_TOKEN
        }
        
        print("\nTesting /me endpoint with basic fields:")
        print(f"Endpoint: {endpoint}")
        print(f"Fields: id,name")
        
        response = requests.get(endpoint, params=params)
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Data: {json.dumps(data, indent=2)}")
            print("✅ Successfully retrieved user data")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing /me endpoint: {e}")
        return False

def test_search(searcher):
    """Test the search functionality"""
    try:
        # Test with a single search term
        test_term = "keynote speaker"
        print(f"\nTesting search with term: {test_term}")
        
        accounts = searcher.search_accounts(test_term, limit=5)
        print(f"Found {len(accounts)} accounts")
        
        if accounts:
            print("\nTesting account filtering...")
            filtered_accounts = searcher.filter_accounts(accounts)
            print(f"Found {len(filtered_accounts)} matching accounts after filtering")
            
            if filtered_accounts:
                print("\nSample account details:")
                account = filtered_accounts[0]
                for key, value in account.items():
                    print(f"{key}: {value}")
                
                return True
        return False
    except Exception as e:
        print(f"❌ Error during search test: {e}")
        return False

def main():
    print("Starting Instagram API Test...")
    print(f"Access Token Status: {'✅ Present' if INSTAGRAM_ACCESS_TOKEN else '❌ Missing'}")
    
    # Test connection
    searcher = test_connection()
    if not searcher:
        return
    
    # Test /me endpoint
    if test_me_endpoint():
        print("\n✅ Basic API test completed successfully!")
    else:
        print("\n❌ API test failed. Please check your access token and permissions.")
    
    # Test search
    if test_search(searcher):
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed. Please check the logs for details.")

if __name__ == "__main__":
    main() 