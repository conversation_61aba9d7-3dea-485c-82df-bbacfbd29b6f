#!/usr/bin/env python3
"""
Run Targeted Instagram Lead Generation

This script generates qualified leads for:
- Keynote Speakers
- Online Coaches/Consultants/Course Creators  
- Authors

Criteria:
- 100-10,000 followers (sweet spot for engagement)
- US-based profiles (extensive location detection)
- Recent posting activity (active accounts only)
- Target industry classification
"""

import argparse
import json
from datetime import datetime
from targeted_lead_generator import TargetedLeadGenerator

def main():
    parser = argparse.ArgumentParser(description='Generate targeted Instagram leads')
    parser.add_argument('--industries', nargs='+', 
                       choices=['keynote_speaker', 'coach_consultant_creator', 'author', 'all'],
                       default=['all'],
                       help='Industries to search for')
    parser.add_argument('--max-leads', type=int, default=10,
                       help='Maximum leads per industry (default: 10)')
    parser.add_argument('--output', type=str,
                       help='Output filename (default: auto-generated)')
    parser.add_argument('--test-mode', action='store_true',
                       help='Run in test mode (fewer API calls)')
    
    args = parser.parse_args()
    
    # Initialize lead generator
    print("🚀 TARGETED INSTAGRAM LEAD GENERATOR")
    print("=" * 60)
    print("Searching for qualified leads with criteria:")
    print("• 100-10,000 followers (engagement sweet spot)")
    print("• US-based profiles")
    print("• Recent posting activity")
    print("• Target industries: Speakers, Coaches, Authors")
    print()
    
    generator = TargetedLeadGenerator()
    
    # Determine industries to search
    if 'all' in args.industries:
        target_industries = ['keynote_speaker', 'coach_consultant_creator', 'author']
    else:
        target_industries = args.industries
    
    # Adjust for test mode
    max_leads = 3 if args.test_mode else args.max_leads
    
    print(f"🎯 Target Industries: {[industry.replace('_', ' ').title() for industry in target_industries]}")
    print(f"📊 Max leads per industry: {max_leads}")
    print(f"🧪 Test mode: {'ON' if args.test_mode else 'OFF'}")
    print()
    
    # Generate leads
    try:
        leads_data = generator.generate_qualified_leads(target_industries, max_leads)
        
        # Calculate totals
        total_leads = sum(len(leads) for leads in leads_data.values())
        
        print("\n" + "=" * 60)
        print("📊 LEAD GENERATION RESULTS")
        print("=" * 60)
        
        for industry, leads in leads_data.items():
            industry_name = industry.replace('_', ' ').title()
            print(f"\n🎯 {industry_name}:")
            print(f"   Qualified Leads: {len(leads)}")
            
            if leads:
                avg_score = sum(lead.get('qualification_score', 0) for lead in leads) / len(leads)
                with_email = sum(1 for lead in leads if lead.get('contact_info', {}).get('email'))
                business_accounts = sum(1 for lead in leads if lead.get('is_business_account'))
                
                print(f"   Avg Qualification Score: {avg_score:.1f}/100")
                print(f"   With Email Contact: {with_email}")
                print(f"   Business Accounts: {business_accounts}")
                
                print(f"   Sample Leads:")
                for i, lead in enumerate(leads[:3], 1):
                    contact = lead.get('contact_info', {})
                    print(f"     {i}. @{lead['username']} ({lead['followers']} followers)")
                    print(f"        Score: {lead.get('qualification_score', 0)}/100")
                    if contact.get('email'):
                        print(f"        Email: {contact['email']}")
                    print(f"        Bio: {lead.get('biography', '')[:60]}...")
        
        print(f"\n🎉 TOTAL QUALIFIED LEADS FOUND: {total_leads}")
        
        if total_leads > 0:
            # Export to CSV (primary format)
            csv_file = generator.export_leads_to_csv(leads_data, args.output)

            # Export to Excel
            excel_file = generator.export_leads_to_excel(leads_data, args.output)

            # Export to JSON for backup
            json_filename = csv_file.replace('.csv', '.json') if csv_file else f"leads_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(leads_data, f, indent=2, ensure_ascii=False, default=str)

            print(f"\n📄 Results exported to:")
            print(f"   CSV: {csv_file}")
            print(f"   Excel: {excel_file}")
            print(f"   JSON: {json_filename}")
            
            print(f"\n✅ SUCCESS! You now have {total_leads} qualified leads ready for outreach!")
            print(f"\n💡 Next steps:")
            print(f"   1. Open {excel_file} to review your leads")
            print(f"   2. Sort by Qualification Score (highest first)")
            print(f"   3. Start outreach with leads that have email contacts")
            print(f"   4. Focus on business accounts for higher response rates")
            
        else:
            print("\n⚠️  No qualified leads found. Try:")
            print("   • Expanding search criteria")
            print("   • Trying different keywords")
            print("   • Running again later (Instagram data changes)")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Search interrupted by user")
        print("Partial results may have been saved")
    except Exception as e:
        print(f"\n❌ Error during lead generation: {e}")
        print("Check the log file for detailed error information")

if __name__ == "__main__":
    main()
