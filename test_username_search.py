#!/usr/bin/env python3
"""
Test script for Instagram Username Search Tool

This script tests the basic functionality of the username searcher
without making too many API calls.
"""

import sys
import logging
from username_searcher import UsernameSearcher
from username_search_config import INDUSTRY_SEARCH_CONFIGS

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import requests
        import googlesearch
        import instaloader
        import pandas as pd
        import sklearn
        from fake_useragent import UserAgent
        print("✅ All required modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install missing dependencies with: pip install -r requirements.txt")
        return False

def test_username_generation():
    """Test username generation functionality"""
    print("\n🧪 Testing username generation...")
    
    try:
        searcher = UsernameSearcher()
        
        # Test basic username generation
        base_name = "john smith"
        variations = searcher.generate_username_variations(base_name, industry="speaker")
        
        print(f"Generated {len(variations)} variations for '{base_name}':")
        for i, variation in enumerate(variations[:10], 1):
            print(f"  {i}. {variation}")
        
        print("✅ Username generation test passed")
        return True
    except Exception as e:
        print(f"❌ Username generation test failed: {e}")
        return False

def test_email_extraction():
    """Test email extraction from bio"""
    print("\n🧪 Testing email extraction...")
    
    try:
        searcher = UsernameSearcher()
        
        test_bios = [
            "Contact <NAME_EMAIL> for business inquiries",
            "Email: <EMAIL> | Speaker & Coach",
            "No email here, just a regular bio",
            "Reach out: <EMAIL> or call me"
        ]
        
        for bio in test_bios:
            email = searcher.extract_email_from_bio(bio)
            print(f"Bio: '{bio[:50]}...'")
            print(f"Email: {email or 'None found'}")
            print()
        
        print("✅ Email extraction test passed")
        return True
    except Exception as e:
        print(f"❌ Email extraction test failed: {e}")
        return False

def test_industry_classification():
    """Test industry classification"""
    print("\n🧪 Testing industry classification...")
    
    try:
        searcher = UsernameSearcher()
        
        test_bios = [
            "Keynote speaker and motivational coach helping businesses grow",
            "Published author of 3 bestselling books on leadership",
            "Online course creator teaching digital marketing strategies",
            "Business consultant with 10+ years experience",
            "Fitness trainer and nutrition coach"
        ]
        
        for bio in test_bios:
            industry = searcher.classify_industry(bio)
            print(f"Bio: '{bio}'")
            print(f"Classified as: {industry}")
            print()
        
        print("✅ Industry classification test passed")
        return True
    except Exception as e:
        print(f"❌ Industry classification test failed: {e}")
        return False

def test_google_search_simulation():
    """Test Google search URL extraction (without actual search)"""
    print("\n🧪 Testing URL extraction...")
    
    try:
        searcher = UsernameSearcher()
        
        test_urls = [
            "https://www.instagram.com/johnsmith/",
            "https://instagram.com/jane_doe",
            "https://www.instagram.com/p/ABC123/",  # Should be filtered out
            "https://www.instagram.com/reel/XYZ789/",  # Should be filtered out
            "https://www.instagram.com/business_coach_pro/",
            "https://www.instagram.com/stories/highlights/123/"  # Should be filtered out
        ]
        
        for url in test_urls:
            username = searcher.extract_username_from_url(url)
            print(f"URL: {url}")
            print(f"Username: {username or 'Filtered out'}")
            print()
        
        print("✅ URL extraction test passed")
        return True
    except Exception as e:
        print(f"❌ URL extraction test failed: {e}")
        return False

def test_configuration_loading():
    """Test configuration loading"""
    print("\n🧪 Testing configuration loading...")
    
    try:
        # Test that configurations are properly structured
        for industry, config in INDUSTRY_SEARCH_CONFIGS.items():
            assert 'keywords' in config, f"Missing keywords for {industry}"
            assert 'criteria' in config, f"Missing criteria for {industry}"
            assert len(config['keywords']) > 0, f"No keywords for {industry}"
            print(f"✅ {industry}: {len(config['keywords'])} keywords, criteria defined")
        
        print("✅ Configuration loading test passed")
        return True
    except Exception as e:
        print(f"❌ Configuration loading test failed: {e}")
        return False

def test_basic_functionality():
    """Test basic searcher initialization and methods"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test initialization
        searcher = UsernameSearcher()
        print("✅ Searcher initialized successfully")
        
        # Test rate limiting
        searcher.rate_limit()
        print("✅ Rate limiting works")
        
        # Test header generation
        headers = searcher.get_random_headers()
        assert 'User-Agent' in headers, "Missing User-Agent header"
        print("✅ Header generation works")
        
        print("✅ Basic functionality test passed")
        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Username Search Tool Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_configuration_loading,
        test_username_generation,
        test_email_extraction,
        test_industry_classification,
        test_google_search_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The username search tool is ready to use.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run a search: python run_username_search.py --mode search --keywords 'keynote speaker'")
        print("3. Check availability: python run_username_search.py --mode availability --names 'john smith'")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
