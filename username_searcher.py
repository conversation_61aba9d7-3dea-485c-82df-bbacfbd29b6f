import requests
import time
import random
import re
import logging
from typing import List, Dict, Optional, Set
from googlesearch import search
import instaloader
from fake_useragent import UserAgent
import pandas as pd
from datetime import datetime
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
import pickle
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('username_search.log'),
        logging.StreamHandler()
    ]
)

class UsernameSearcher:
    def __init__(self, use_proxies: bool = False, proxy_list: List[str] = None):
        """
        Initialize the username searcher
        
        Args:
            use_proxies: Whether to use proxy rotation
            proxy_list: List of proxy URLs (format: 'http://ip:port')
        """
        self.logger = logging.getLogger(__name__)
        self.ua = UserAgent()
        self.use_proxies = use_proxies
        self.proxy_list = proxy_list or []
        self.current_proxy_index = 0
        
        # Initialize instaloader
        self.L = instaloader.Instaloader(
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Industry classification model
        self.industry_classifier = None
        self.load_or_train_classifier()
        
        # Rate limiting
        self.last_request_time = 0
        self.min_delay = 2  # Minimum delay between requests
        
    def get_random_headers(self) -> Dict[str, str]:
        """Get random headers for requests"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """Get next proxy from rotation"""
        if not self.use_proxies or not self.proxy_list:
            return None
            
        proxy = self.proxy_list[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_list)
        
        return {
            'http': proxy,
            'https': proxy
        }
    
    def rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_delay:
            sleep_time = self.min_delay - time_since_last + random.uniform(0.5, 2.0)
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def search_google_for_profiles(self, keywords: List[str], num_results: int = 50) -> Set[str]:
        """
        Search Google for Instagram profiles containing specific keywords
        
        Args:
            keywords: List of keywords to search for
            num_results: Number of results to return per keyword
            
        Returns:
            Set of Instagram usernames
        """
        usernames = set()
        
        for keyword in keywords:
            try:
                self.logger.info(f"Searching Google for Instagram profiles with keyword: {keyword}")
                
                # Create search query for Instagram profiles
                query = f'site:instagram.com "{keyword}" -inurl:p/ -inurl:reel/ -inurl:tv/ -inurl:stories/'
                
                self.rate_limit()
                
                # Search Google
                search_results = search(
                    query, 
                    num_results=num_results, 
                    sleep_interval=random.uniform(2, 5),
                    lang='en'
                )
                
                # Extract usernames from URLs
                for url in search_results:
                    username = self.extract_username_from_url(url)
                    if username:
                        usernames.add(username)
                        
                self.logger.info(f"Found {len(usernames)} unique usernames so far")
                
            except Exception as e:
                self.logger.error(f"Error searching for keyword '{keyword}': {e}")
                continue
        
        return usernames
    
    def extract_username_from_url(self, url: str) -> Optional[str]:
        """Extract username from Instagram URL"""
        # Pattern to match Instagram profile URLs
        pattern = r'https?://(?:www\.)?instagram\.com/([^/?#&]+)/?'
        match = re.match(pattern, url)
        
        if match:
            username = match.group(1)
            # Filter out non-username paths
            invalid_paths = {'p', 'reel', 'tv', 'stories', 'explore', 'accounts', 'direct'}
            if username not in invalid_paths and not username.startswith('p/'):
                return username
        
        return None
    
    def verify_username_with_instaloader(self, username: str) -> Optional[Dict]:
        """
        Verify username exists and get profile data using instaloader
        
        Args:
            username: Instagram username to verify
            
        Returns:
            Dictionary with profile data or None if not found/error
        """
        try:
            self.rate_limit()
            
            # Get profile
            profile = instaloader.Profile.from_username(self.L.context, username)
            
            # Extract relevant data
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'followees': profile.followees,
                'posts_count': profile.mediacount,
                'is_private': profile.is_private,
                'is_verified': profile.is_verified,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'business_category_name': getattr(profile, 'business_category_name', None)
            }
            
            return profile_data
            
        except instaloader.exceptions.ProfileNotExistsException:
            self.logger.debug(f"Profile {username} does not exist")
            return None
        except instaloader.exceptions.ConnectionException as e:
            self.logger.warning(f"Connection error for {username}: {e}")
            time.sleep(random.uniform(5, 10))  # Longer delay on connection issues
            return None
        except Exception as e:
            self.logger.error(f"Error verifying username {username}: {e}")
            return None
    
    def load_or_train_classifier(self):
        """Load existing classifier or train a new one"""
        classifier_path = 'industry_classifier.pkl'
        
        if os.path.exists(classifier_path):
            try:
                with open(classifier_path, 'rb') as f:
                    self.industry_classifier = pickle.load(f)
                self.logger.info("Loaded existing industry classifier")
                return
            except Exception as e:
                self.logger.warning(f"Error loading classifier: {e}")
        
        # Train new classifier with sample data
        self.train_industry_classifier()
        
        # Save classifier
        try:
            with open(classifier_path, 'wb') as f:
                pickle.dump(self.industry_classifier, f)
            self.logger.info("Saved new industry classifier")
        except Exception as e:
            self.logger.error(f"Error saving classifier: {e}")
    
    def train_industry_classifier(self):
        """Train industry classification model"""
        # Sample training data (in real implementation, you'd have more data)
        training_data = [
            ("keynote speaker motivational talks", "speaker"),
            ("business coach consultant mentor", "coach"),
            ("online course creator training", "course_creator"),
            ("author writer published book", "author"),
            ("fitness trainer workout", "fitness"),
            ("photographer visual artist", "creative"),
            ("entrepreneur startup founder", "business"),
            ("chef cooking recipes", "food"),
            ("travel blogger adventure", "travel"),
            ("fashion stylist designer", "fashion")
        ]
        
        texts = [item[0] for item in training_data]
        labels = [item[1] for item in training_data]
        
        # Create pipeline with TF-IDF and Naive Bayes
        self.industry_classifier = Pipeline([
            ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
            ('classifier', MultinomialNB())
        ])
        
        self.industry_classifier.fit(texts, labels)
        self.logger.info("Trained new industry classifier")
    
    def classify_industry(self, bio: str, business_category: str = None) -> str:
        """Classify user industry based on bio and business category"""
        if not bio:
            return "unknown"

        try:
            # Combine bio and business category for classification
            text_to_classify = bio.lower()
            if business_category:
                text_to_classify += f" {business_category.lower()}"

            prediction = self.industry_classifier.predict([text_to_classify])[0]
            return prediction
        except Exception as e:
            self.logger.error(f"Error classifying industry: {e}")
            return "unknown"

    def filter_profiles(self, profiles: List[Dict], criteria: Dict) -> List[Dict]:
        """
        Filter profiles based on specified criteria

        Args:
            profiles: List of profile dictionaries
            criteria: Dictionary with filtering criteria

        Returns:
            List of filtered profiles
        """
        filtered = []

        for profile in profiles:
            try:
                # Check follower count
                followers = profile.get('followers', 0)
                if 'min_followers' in criteria and followers < criteria['min_followers']:
                    continue
                if 'max_followers' in criteria and followers > criteria['max_followers']:
                    continue

                # Check if account is private
                if criteria.get('exclude_private', True) and profile.get('is_private', False):
                    continue

                # Check if account has posts
                if criteria.get('min_posts', 0) > profile.get('posts_count', 0):
                    continue

                # Check industry classification
                if 'target_industries' in criteria:
                    industry = self.classify_industry(
                        profile.get('biography', ''),
                        profile.get('business_category_name')
                    )
                    if industry not in criteria['target_industries']:
                        continue

                # Check for email in bio
                if criteria.get('require_email', False):
                    bio = profile.get('biography', '')
                    if not self.extract_email_from_bio(bio):
                        continue

                # Add industry classification to profile
                profile['classified_industry'] = self.classify_industry(
                    profile.get('biography', ''),
                    profile.get('business_category_name')
                )

                # Extract additional data
                profile['email'] = self.extract_email_from_bio(profile.get('biography', ''))
                profile['phone'] = self.extract_phone_from_bio(profile.get('biography', ''))

                filtered.append(profile)

            except Exception as e:
                self.logger.error(f"Error filtering profile {profile.get('username', 'unknown')}: {e}")
                continue

        return filtered

    def extract_email_from_bio(self, bio: str) -> Optional[str]:
        """Extract email address from biography"""
        if not bio:
            return None

        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        match = re.search(email_pattern, bio)
        return match.group(0) if match else None

    def extract_phone_from_bio(self, bio: str) -> Optional[str]:
        """Extract phone number from biography"""
        if not bio:
            return None

        # Pattern for US phone numbers
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',
            r'\+1[-.\s]?\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, bio)
            if match:
                return match.group(0)

        return None

    def check_username_availability(self, username: str) -> bool:
        """
        Check if a username is available (doesn't exist)

        Args:
            username: Username to check

        Returns:
            True if available, False if taken
        """
        try:
            self.rate_limit()
            instaloader.Profile.from_username(self.L.context, username)
            return False  # Username exists, so not available
        except instaloader.exceptions.ProfileNotExistsException:
            return True  # Username doesn't exist, so available
        except Exception as e:
            self.logger.error(f"Error checking availability for {username}: {e}")
            return False  # Assume not available on error

    def generate_username_variations(self, base_name: str, industry: str = None) -> List[str]:
        """
        Generate username variations based on a base name

        Args:
            base_name: Base name to generate variations from
            industry: Industry context for variations

        Returns:
            List of username variations
        """
        variations = []
        base_clean = re.sub(r'[^a-zA-Z0-9]', '', base_name.lower())

        # Basic variations
        variations.extend([
            base_clean,
            f"{base_clean}_official",
            f"the{base_clean}",
            f"{base_clean}co",
            f"{base_clean}hq"
        ])

        # Industry-specific variations
        if industry:
            industry_suffixes = {
                'speaker': ['speaks', 'talks', 'keynote'],
                'coach': ['coaching', 'coach', 'mentor'],
                'author': ['writes', 'author', 'books'],
                'course_creator': ['teaches', 'academy', 'courses'],
                'business': ['biz', 'company', 'ventures']
            }

            if industry in industry_suffixes:
                for suffix in industry_suffixes[industry]:
                    variations.extend([
                        f"{base_clean}{suffix}",
                        f"{base_clean}_{suffix}"
                    ])

        # Add numbers
        for i in range(1, 10):
            variations.extend([
                f"{base_clean}{i}",
                f"{base_clean}_{i}"
            ])

        return list(set(variations))  # Remove duplicates

    def search_and_verify_usernames(self, keywords: List[str], criteria: Dict, num_results: int = 50) -> List[Dict]:
        """
        Complete workflow: search, verify, and filter usernames

        Args:
            keywords: List of keywords to search for
            criteria: Filtering criteria
            num_results: Number of results per keyword

        Returns:
            List of verified and filtered profiles
        """
        self.logger.info(f"Starting username search for keywords: {keywords}")

        # Step 1: Search Google for potential usernames
        usernames = self.search_google_for_profiles(keywords, num_results)
        self.logger.info(f"Found {len(usernames)} potential usernames from Google search")

        # Step 2: Verify usernames with instaloader
        verified_profiles = []
        for i, username in enumerate(usernames, 1):
            self.logger.info(f"Verifying username {i}/{len(usernames)}: {username}")

            profile_data = self.verify_username_with_instaloader(username)
            if profile_data:
                verified_profiles.append(profile_data)
                self.logger.info(f"✅ Verified: {username} ({profile_data.get('followers', 0)} followers)")
            else:
                self.logger.debug(f"❌ Could not verify: {username}")

        self.logger.info(f"Verified {len(verified_profiles)} profiles")

        # Step 3: Filter profiles based on criteria
        filtered_profiles = self.filter_profiles(verified_profiles, criteria)
        self.logger.info(f"Filtered to {len(filtered_profiles)} profiles matching criteria")

        return filtered_profiles

    def export_to_excel(self, profiles: List[Dict], filename: str = None) -> str:
        """
        Export profiles to Excel file

        Args:
            profiles: List of profile dictionaries
            filename: Output filename (optional)

        Returns:
            Path to created Excel file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"instagram_usernames_{timestamp}.xlsx"

        # Prepare data for Excel
        excel_data = []
        for profile in profiles:
            excel_data.append({
                'Username': profile.get('username', ''),
                'Full Name': profile.get('full_name', ''),
                'Industry': profile.get('classified_industry', ''),
                'Followers': profile.get('followers', 0),
                'Following': profile.get('followees', 0),
                'Posts': profile.get('posts_count', 0),
                'Is Verified': profile.get('is_verified', False),
                'Is Business': profile.get('is_business_account', False),
                'Is Private': profile.get('is_private', False),
                'Email': profile.get('email', ''),
                'Phone': profile.get('phone', ''),
                'Biography': profile.get('biography', ''),
                'External URL': profile.get('external_url', ''),
                'Business Category': profile.get('business_category_name', '')
            })

        # Create DataFrame and export
        df = pd.DataFrame(excel_data)
        df.to_excel(filename, index=False, engine='openpyxl')

        self.logger.info(f"Exported {len(profiles)} profiles to {filename}")
        return filename

    def export_to_json(self, profiles: List[Dict], filename: str = None) -> str:
        """
        Export profiles to JSON file

        Args:
            profiles: List of profile dictionaries
            filename: Output filename (optional)

        Returns:
            Path to created JSON file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"instagram_usernames_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(profiles, f, indent=2, ensure_ascii=False, default=str)

        self.logger.info(f"Exported {len(profiles)} profiles to {filename}")
        return filename

    def find_available_usernames(self, base_names: List[str], industry: str = None, max_variations: int = 10) -> List[Dict]:
        """
        Find available username variations for given base names

        Args:
            base_names: List of base names to generate variations from
            industry: Industry context for variations
            max_variations: Maximum variations to check per base name

        Returns:
            List of available usernames with metadata
        """
        available_usernames = []

        for base_name in base_names:
            self.logger.info(f"Checking availability for variations of: {base_name}")

            variations = self.generate_username_variations(base_name, industry)[:max_variations]

            for variation in variations:
                if self.check_username_availability(variation):
                    available_usernames.append({
                        'username': variation,
                        'base_name': base_name,
                        'industry': industry,
                        'available': True,
                        'checked_at': datetime.now().isoformat()
                    })
                    self.logger.info(f"✅ Available: {variation}")
                else:
                    self.logger.debug(f"❌ Taken: {variation}")

        return available_usernames
