"""
Configuration file for Instagram Username Search Tool

This file contains default settings and predefined search configurations
for different use cases and industries.
"""

# Default search criteria for different industries
INDUSTRY_SEARCH_CONFIGS = {
    'speaker': {
        'keywords': [
            'keynote speaker',
            'motivational speaker', 
            'public speaker',
            'conference speaker',
            'TEDx speaker',
            'speaker bureau'
        ],
        'criteria': {
            'min_followers': 500,
            'max_followers': 50000,
            'exclude_private': True,
            'min_posts': 10,
            'target_industries': ['speaker'],
            'require_email': False
        }
    },
    
    'coach': {
        'keywords': [
            'business coach',
            'life coach',
            'executive coach',
            'leadership coach',
            'career coach',
            'online coach'
        ],
        'criteria': {
            'min_followers': 200,
            'max_followers': 25000,
            'exclude_private': True,
            'min_posts': 15,
            'target_industries': ['coach'],
            'require_email': False
        }
    },
    
    'author': {
        'keywords': [
            'author',
            'writer',
            'novelist',
            'bestselling author',
            'published author',
            'book author'
        ],
        'criteria': {
            'min_followers': 100,
            'max_followers': 100000,
            'exclude_private': True,
            'min_posts': 5,
            'target_industries': ['author'],
            'require_email': False
        }
    },
    
    'course_creator': {
        'keywords': [
            'course creator',
            'online course',
            'masterclass',
            'training program',
            'educational content',
            'online educator'
        ],
        'criteria': {
            'min_followers': 300,
            'max_followers': 30000,
            'exclude_private': True,
            'min_posts': 20,
            'target_industries': ['course_creator'],
            'require_email': False
        }
    },
    
    'consultant': {
        'keywords': [
            'consultant',
            'business consultant',
            'strategy consultant',
            'management consultant',
            'freelance consultant',
            'consulting services'
        ],
        'criteria': {
            'min_followers': 150,
            'max_followers': 20000,
            'exclude_private': True,
            'min_posts': 8,
            'target_industries': ['business'],
            'require_email': True
        }
    }
}

# Username generation patterns for different industries
USERNAME_PATTERNS = {
    'speaker': {
        'prefixes': ['the', 'keynote', 'speaker'],
        'suffixes': ['speaks', 'talks', 'keynote', 'speaker', 'voice', 'stage'],
        'separators': ['', '_', '.']
    },
    
    'coach': {
        'prefixes': ['coach', 'mentor', 'guide'],
        'suffixes': ['coach', 'coaching', 'mentor', 'guide', 'transform', 'growth'],
        'separators': ['', '_', '.']
    },
    
    'author': {
        'prefixes': ['author', 'writer', 'the'],
        'suffixes': ['writes', 'author', 'books', 'stories', 'words', 'pen'],
        'separators': ['', '_', '.']
    },
    
    'course_creator': {
        'prefixes': ['teach', 'learn', 'edu'],
        'suffixes': ['teaches', 'academy', 'courses', 'learn', 'edu', 'school'],
        'separators': ['', '_', '.']
    },
    
    'business': {
        'prefixes': ['biz', 'pro', 'expert'],
        'suffixes': ['biz', 'pro', 'expert', 'consulting', 'solutions', 'ventures'],
        'separators': ['', '_', '.']
    }
}

# Common professional keywords for bio analysis
PROFESSIONAL_KEYWORDS = {
    'speaker': [
        'keynote', 'speaker', 'speaking', 'talks', 'presentations', 'motivational',
        'conference', 'event', 'stage', 'audience', 'inspire', 'tedx'
    ],
    
    'coach': [
        'coach', 'coaching', 'mentor', 'mentoring', 'consultant', 'consulting',
        'transformation', 'growth', 'development', 'success', 'leadership'
    ],
    
    'author': [
        'author', 'writer', 'book', 'books', 'published', 'bestseller',
        'novel', 'writing', 'stories', 'amazon', 'kindle'
    ],
    
    'course_creator': [
        'course', 'courses', 'training', 'education', 'teach', 'instructor',
        'masterclass', 'workshop', 'program', 'learning', 'academy'
    ],
    
    'fitness': [
        'fitness', 'trainer', 'workout', 'gym', 'health', 'nutrition',
        'personal trainer', 'exercise', 'wellness', 'strength'
    ],
    
    'creative': [
        'artist', 'designer', 'photographer', 'creative', 'art', 'design',
        'visual', 'graphic', 'portfolio', 'studio'
    ],
    
    'food': [
        'chef', 'cooking', 'recipe', 'food', 'culinary', 'kitchen',
        'restaurant', 'baker', 'nutrition', 'foodie'
    ],
    
    'travel': [
        'travel', 'traveler', 'adventure', 'explore', 'wanderlust',
        'journey', 'destination', 'blogger', 'nomad'
    ],
    
    'fashion': [
        'fashion', 'style', 'stylist', 'designer', 'model', 'beauty',
        'outfit', 'trends', 'boutique', 'brand'
    ]
}

# Rate limiting settings
RATE_LIMITS = {
    'google_search_delay': (2, 5),      # Random delay between 2-5 seconds
    'instaloader_delay': (1, 3),        # Random delay between 1-3 seconds
    'availability_check_delay': (0.5, 2), # Random delay between 0.5-2 seconds
    'max_requests_per_minute': 30,       # Maximum requests per minute
    'cooldown_on_error': 10              # Cooldown time on errors (seconds)
}

# Export settings
EXPORT_SETTINGS = {
    'excel_columns': [
        'Username', 'Full Name', 'Industry', 'Followers', 'Following',
        'Posts', 'Is Verified', 'Is Business', 'Is Private', 'Email',
        'Phone', 'Biography', 'External URL', 'Business Category'
    ],
    'max_bio_length': 200,               # Truncate bio to this length in exports
    'include_timestamps': True,          # Include search timestamps
    'separate_sheets_by_industry': True  # Create separate Excel sheets per industry
}

# Proxy settings (if using proxy rotation)
PROXY_SETTINGS = {
    'rotation_interval': 10,             # Change proxy every N requests
    'timeout': 30,                       # Proxy timeout in seconds
    'max_retries': 3,                    # Max retries per proxy
    'test_url': 'https://httpbin.org/ip' # URL to test proxy connectivity
}

# Quality filters
QUALITY_FILTERS = {
    'min_engagement_rate': 0.01,         # Minimum engagement rate (1%)
    'max_engagement_rate': 0.15,         # Maximum engagement rate (15%)
    'min_bio_length': 10,                # Minimum biography length
    'exclude_keywords': [                # Keywords to exclude from results
        'spam', 'fake', 'bot', 'follow4follow', 'f4f', 'l4l'
    ],
    'require_profile_picture': True,     # Require profile picture
    'max_following_ratio': 5.0           # Max following/followers ratio
}

# Default search parameters
DEFAULT_SEARCH_PARAMS = {
    'num_results_per_keyword': 50,
    'max_profiles_to_verify': 500,
    'enable_machine_learning': True,
    'save_intermediate_results': True,
    'use_cache': True,
    'cache_duration_hours': 24
}
