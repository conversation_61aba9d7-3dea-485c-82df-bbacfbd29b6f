#!/usr/bin/env python3
"""
Automatic CSV Lead Generator
Runs automatically and exports Instagram leads to CSV format
"""

import csv
import time
import random
from datetime import datetime
from targeted_lead_generator import TargetedLeadGenerator

def export_to_csv(leads, filename):
    """Export leads to CSV format"""
    headers = [
        'Username',
        'Full_Name', 
        'Industry',
        'Followers',
        'Posts',
        'Qualification_Score',
        'Email',
        'Phone',
        'Website',
        'Is_Business_Account',
        'Is_Verified',
        'Days_Since_Last_Post',
        'Biography',
        'Profile_URL'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)
        
        for lead in leads:
            contact = lead.get('contact_info', {})
            
            row = [
                f"@{lead['username']}",
                lead.get('full_name', ''),
                lead.get('classified_industry', '').replace('_', ' ').title(),
                lead.get('followers', 0),
                lead.get('posts_count', 0),
                lead.get('qualification_score', 0),
                contact.get('email', ''),
                contact.get('phone', ''),
                contact.get('website', ''),
                'Yes' if lead.get('is_business_account') else 'No',
                'Yes' if lead.get('is_verified') else 'No',
                lead.get('days_since_last_post', ''),
                lead.get('biography', '').replace('\n', ' ').replace('\r', ' ')[:200],
                f"https://instagram.com/{lead['username']}"
            ]
            
            writer.writerow(row)
    
    return filename

def search_keynote_speakers():
    """Search for keynote speakers and export to CSV"""
    print("🎯 SEARCHING FOR KEYNOTE SPEAKER LEADS")
    print("=" * 50)
    print("Criteria: 100-10k followers, US-based, recent activity")
    print()
    
    generator = TargetedLeadGenerator()
    
    # Search Google
    print("Step 1: Searching Google for keynote speaker profiles...")
    usernames = generator.search_google_for_industry('keynote_speaker', num_results=25)
    
    if not usernames:
        print("No usernames found")
        return []
    
    print(f"Found {len(usernames)} potential usernames")
    print()
    
    # Verify profiles
    print("Step 2: Verifying profiles...")
    qualified_leads = []
    
    for i, username in enumerate(usernames[:15], 1):  # Check first 15
        print(f"[{i}/15] @{username}...", end=' ')
        
        try:
            profile = generator.verify_and_qualify_profile(username)
            
            if profile:
                qualified_leads.append(profile)
                score = profile.get('qualification_score', 0)
                followers = profile.get('followers', 0)
                email = profile.get('contact_info', {}).get('email', '')
                email_icon = "📧" if email else "❌"
                
                print(f"✅ QUALIFIED! Score: {score}/100, Followers: {followers:,} {email_icon}")
                
                if len(qualified_leads) >= 5:  # Stop at 5 qualified leads
                    print("Found 5 qualified leads - stopping")
                    break
            else:
                print("❌ Not qualified")
                
        except Exception as e:
            print(f"⚠️ Error")
            continue
    
    return qualified_leads

def main():
    """Main function"""
    print("📊 AUTOMATIC CSV LEAD GENERATOR")
    print("=" * 60)
    print("Automatically searches for qualified Instagram leads")
    print("Exports results to CSV format for Excel/CRM import")
    print()
    
    try:
        # Search for keynote speakers
        leads = search_keynote_speakers()
        
        if leads:
            # Sort by qualification score
            leads.sort(key=lambda x: x.get('qualification_score', 0), reverse=True)
            
            # Generate CSV filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"keynote_speakers_{timestamp}.csv"
            
            # Export to CSV
            export_to_csv(leads, csv_filename)
            
            print()
            print("=" * 60)
            print(f"✅ SUCCESS! Found {len(leads)} qualified keynote speaker leads")
            print("=" * 60)
            
            # Display results
            print(f"\n🌟 TOP QUALIFIED LEADS:")
            for i, lead in enumerate(leads, 1):
                contact = lead.get('contact_info', {})
                print(f"\n{i}. @{lead['username']} (Score: {lead.get('qualification_score', 0)}/100)")
                print(f"   Name: {lead.get('full_name', 'N/A')}")
                print(f"   Followers: {lead.get('followers', 0):,}")
                print(f"   Email: {contact.get('email', 'None')}")
                print(f"   Website: {contact.get('website', 'None')}")
                print(f"   Bio: {lead.get('biography', '')[:60]}...")
            
            # Show stats
            with_email = sum(1 for lead in leads if lead.get('contact_info', {}).get('email'))
            business_accounts = sum(1 for lead in leads if lead.get('is_business_account'))
            avg_score = sum(lead.get('qualification_score', 0) for lead in leads) / len(leads)
            
            print(f"\n📊 LEAD QUALITY STATS:")
            print(f"   • CSV File: {csv_filename}")
            print(f"   • Average qualification score: {avg_score:.1f}/100")
            print(f"   • Leads with email: {with_email}/{len(leads)}")
            print(f"   • Business accounts: {business_accounts}/{len(leads)}")
            
            print(f"\n🎯 NEXT STEPS:")
            print(f"1. Open {csv_filename} in Excel or Google Sheets")
            print(f"2. Sort by 'Qualification_Score' column (highest first)")
            print(f"3. Filter for leads with email addresses")
            print(f"4. Start outreach with highest-scoring leads")
            
            return csv_filename
        else:
            print("❌ No qualified leads found in this search")
            print("Try running again later or adjusting search criteria")
            return None
            
    except KeyboardInterrupt:
        print("\n⏹️ Search interrupted")
        return None
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return None

if __name__ == "__main__":
    main()
