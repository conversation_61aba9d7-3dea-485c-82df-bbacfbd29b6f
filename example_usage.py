#!/usr/bin/env python3
"""
Example usage of the Instagram Username Search Tool

This script demonstrates the key features without making actual API calls
for testing purposes.
"""

import sys
import os

# Add current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_username_generation():
    """Demonstrate username generation functionality"""
    print("🎯 Username Generation Demo")
    print("=" * 40)
    
    try:
        from username_searcher import UsernameSearcher
        searcher = UsernameSearcher()
        
        # Test cases
        test_cases = [
            ("<PERSON>", "speaker"),
            ("Marketing Pro", "business"),
            ("Jane Author", "author"),
            ("Fitness Coach", "coach")
        ]
        
        for base_name, industry in test_cases:
            print(f"\nBase Name: '{base_name}' | Industry: {industry}")
            variations = searcher.generate_username_variations(base_name, industry)
            
            print("Generated variations:")
            for i, variation in enumerate(variations[:8], 1):
                print(f"  {i}. @{variation}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_email_extraction():
    """Demonstrate email extraction from bios"""
    print("\n📧 Email Extraction Demo")
    print("=" * 40)
    
    try:
        from username_searcher import UsernameSearcher
        searcher = UsernameSearcher()
        
        test_bios = [
            "Keynote speaker | Contact: <EMAIL> | Available worldwide",
            "Business coach helping entrepreneurs grow 📈 Email me: <EMAIL>",
            "Author of 3 bestselling books 📚 DM for collaborations",
            "Marketing consultant | <EMAIL> | 10+ years experience",
            "Fitness trainer 💪 No email here, just motivation!"
        ]
        
        for bio in test_bios:
            email = searcher.extract_email_from_bio(bio)
            print(f"\nBio: '{bio[:60]}...'")
            print(f"Email: {email if email else '❌ None found'}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_industry_classification():
    """Demonstrate industry classification"""
    print("\n🏭 Industry Classification Demo")
    print("=" * 40)
    
    try:
        from username_searcher import UsernameSearcher
        searcher = UsernameSearcher()
        
        test_profiles = [
            "Keynote speaker and leadership expert helping Fortune 500 companies",
            "Published author of 5 books on personal development and success",
            "Online course creator teaching digital marketing to 10k+ students",
            "Certified life coach transforming lives through mindset coaching",
            "Business consultant specializing in startup growth strategies",
            "Fitness trainer and nutrition coach helping clients get in shape",
            "Travel blogger sharing adventures from 50+ countries",
            "Fashion designer creating sustainable clothing for modern women"
        ]
        
        for bio in test_profiles:
            industry = searcher.classify_industry(bio)
            print(f"\nBio: '{bio}'")
            print(f"🏷️  Classified as: {industry}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_search_configuration():
    """Demonstrate search configuration options"""
    print("\n⚙️ Search Configuration Demo")
    print("=" * 40)
    
    try:
        from username_search_config import INDUSTRY_SEARCH_CONFIGS
        
        for industry, config in INDUSTRY_SEARCH_CONFIGS.items():
            print(f"\n🎯 {industry.upper()} Configuration:")
            print(f"   Keywords: {', '.join(config['keywords'][:3])}...")
            print(f"   Min Followers: {config['criteria']['min_followers']:,}")
            print(f"   Max Followers: {config['criteria']['max_followers']:,}")
            print(f"   Min Posts: {config['criteria']['min_posts']}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_url_extraction():
    """Demonstrate URL extraction functionality"""
    print("\n🔗 URL Extraction Demo")
    print("=" * 40)
    
    try:
        from username_searcher import UsernameSearcher
        searcher = UsernameSearcher()
        
        test_urls = [
            "https://www.instagram.com/johnsmith/",
            "https://instagram.com/business_coach_pro",
            "https://www.instagram.com/p/ABC123DEF/",  # Post URL - should be filtered
            "https://www.instagram.com/reel/XYZ789/",   # Reel URL - should be filtered
            "https://www.instagram.com/jane.author/",
            "https://www.instagram.com/stories/highlights/123/",  # Story - should be filtered
            "https://www.instagram.com/marketing_expert_2024/"
        ]
        
        for url in test_urls:
            username = searcher.extract_username_from_url(url)
            status = "✅ Valid" if username else "❌ Filtered out"
            print(f"{status}: {url}")
            if username:
                print(f"         → @{username}")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_filtering_criteria():
    """Demonstrate profile filtering criteria"""
    print("\n🔍 Profile Filtering Demo")
    print("=" * 40)
    
    # Sample profile data
    sample_profiles = [
        {
            'username': 'keynote_speaker_pro',
            'followers': 5000,
            'posts_count': 150,
            'is_private': False,
            'biography': 'Professional keynote speaker helping businesses grow',
            'is_business_account': True
        },
        {
            'username': 'private_coach',
            'followers': 2000,
            'posts_count': 50,
            'is_private': True,
            'biography': 'Life coach and mentor',
            'is_business_account': False
        },
        {
            'username': 'mega_influencer',
            'followers': 500000,
            'posts_count': 1000,
            'is_private': False,
            'biography': 'Lifestyle influencer and brand ambassador',
            'is_business_account': True
        }
    ]
    
    # Filtering criteria
    criteria = {
        'min_followers': 1000,
        'max_followers': 50000,
        'exclude_private': True,
        'min_posts': 10,
        'target_industries': ['speaker', 'coach']
    }
    
    print("Sample profiles to filter:")
    for profile in sample_profiles:
        print(f"\n📋 @{profile['username']}")
        print(f"   Followers: {profile['followers']:,}")
        print(f"   Posts: {profile['posts_count']}")
        print(f"   Private: {profile['is_private']}")
        print(f"   Bio: {profile['biography']}")
        
        # Check against criteria
        passes = True
        reasons = []
        
        if profile['followers'] < criteria['min_followers']:
            passes = False
            reasons.append("Too few followers")
        
        if profile['followers'] > criteria['max_followers']:
            passes = False
            reasons.append("Too many followers")
        
        if criteria['exclude_private'] and profile['is_private']:
            passes = False
            reasons.append("Private account")
        
        if profile['posts_count'] < criteria['min_posts']:
            passes = False
            reasons.append("Too few posts")
        
        if passes:
            print("   ✅ PASSES all criteria")
        else:
            print(f"   ❌ FILTERED: {', '.join(reasons)}")
    
    return True

def main():
    """Run all demonstrations"""
    print("🚀 Instagram Username Search Tool - Feature Demonstration")
    print("=" * 60)
    
    demos = [
        demo_username_generation,
        demo_email_extraction,
        demo_industry_classification,
        demo_search_configuration,
        demo_url_extraction,
        demo_filtering_criteria
    ]
    
    success_count = 0
    
    for demo in demos:
        try:
            if demo():
                success_count += 1
            else:
                print("❌ Demo failed")
        except Exception as e:
            print(f"❌ Demo failed with error: {e}")
    
    print(f"\n🎉 Demonstration Complete!")
    print(f"📊 {success_count}/{len(demos)} demos ran successfully")
    
    if success_count == len(demos):
        print("\n✅ All features working correctly!")
        print("\n🚀 Ready to use the username search tool:")
        print("   • Run: python run_username_search.py --help")
        print("   • Test: python test_username_search.py")
        print("   • Example: python run_username_search.py --mode search --keywords 'keynote speaker'")
    else:
        print("\n⚠️  Some features may need attention. Check error messages above.")

if __name__ == "__main__":
    main()
