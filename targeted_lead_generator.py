#!/usr/bin/env python3
"""
Targeted Instagram Lead Generator
Focused on: Keynote Speakers, Online Coaches/Consultants/Course Creators, Authors
Criteria: 100-10k followers, US-based, recent activity, target industries
"""

import re
import time
import random
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import instaloader
from googlesearch import search
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('targeted_leads.log'),
        logging.StreamHandler()
    ]
)

class TargetedLeadGenerator:
    def __init__(self):
        """Initialize the targeted lead generator"""
        self.logger = logging.getLogger(__name__)
        
        # Initialize instaloader
        self.L = instaloader.Instaloader(
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Target criteria
        self.FOLLOWER_RANGE = {'min': 100, 'max': 10000}
        self.ACTIVITY_THRESHOLD_DAYS = 30
        
        # US location indicators (extensive list)
        self.US_INDICATORS = [
            # States (full names)
            'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado',
            'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho',
            'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana',
            'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota',
            'mississippi', 'missouri', 'montana', 'nebraska', 'nevada',
            'new hampshire', 'new jersey', 'new mexico', 'new york',
            'north carolina', 'north dakota', 'ohio', 'oklahoma', 'oregon',
            'pennsylvania', 'rhode island', 'south carolina', 'south dakota',
            'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington',
            'west virginia', 'wisconsin', 'wyoming',
            
            # State abbreviations
            'al', 'ak', 'az', 'ar', 'ca', 'co', 'ct', 'de', 'fl', 'ga', 'hi',
            'id', 'il', 'in', 'ia', 'ks', 'ky', 'la', 'me', 'md', 'ma', 'mi',
            'mn', 'ms', 'mo', 'mt', 'ne', 'nv', 'nh', 'nj', 'nm', 'ny', 'nc',
            'nd', 'oh', 'ok', 'or', 'pa', 'ri', 'sc', 'sd', 'tn', 'tx', 'ut',
            'vt', 'va', 'wa', 'wv', 'wi', 'wy',
            
            # Major cities
            'new york', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia',
            'san antonio', 'san diego', 'dallas', 'san jose', 'austin', 'jacksonville',
            'fort worth', 'columbus', 'charlotte', 'san francisco', 'indianapolis',
            'seattle', 'denver', 'washington dc', 'boston', 'el paso', 'detroit',
            'nashville', 'portland', 'memphis', 'oklahoma city', 'las vegas',
            'louisville', 'baltimore', 'milwaukee', 'albuquerque', 'tucson',
            'fresno', 'mesa', 'sacramento', 'atlanta', 'kansas city', 'colorado springs',
            'miami', 'raleigh', 'omaha', 'long beach', 'virginia beach', 'oakland',
            'minneapolis', 'tulsa', 'arlington', 'tampa', 'new orleans', 'wichita',
            'cleveland', 'bakersfield', 'aurora', 'anaheim', 'honolulu', 'santa ana',
            'riverside', 'corpus christi', 'lexington', 'stockton', 'henderson',
            'saint paul', 'st. paul', 'cincinnati', 'pittsburgh',
            
            # General US indicators
            'usa', 'united states', 'america', 'us', 'u.s.', 'u.s.a.',
            'american', 'nationwide', 'coast to coast', 'based in us',
            'us based', 'american speaker', 'american author', 'american coach'
        ]
        
        # Target industry keywords
        self.INDUSTRY_KEYWORDS = {
            'keynote_speaker': [
                'keynote speaker', 'keynote', 'public speaker', 'motivational speaker',
                'conference speaker', 'speaker', 'speaking', 'talks', 'presentations',
                'tedx speaker', 'ted speaker', 'professional speaker', 'inspirational speaker',
                'corporate speaker', 'event speaker', 'workshop leader', 'seminar leader',
                'thought leader', 'industry expert', 'speaking engagements', 'book speaking',
                'available for speaking', 'speaker bureau', 'speaking topics'
            ],
            
            'coach_consultant_creator': [
                'business coach', 'life coach', 'executive coach', 'leadership coach',
                'career coach', 'success coach', 'mindset coach', 'performance coach',
                'consultant', 'consulting', 'business consultant', 'strategy consultant',
                'management consultant', 'course creator', 'online course', 'masterclass',
                'training program', 'online coaching', 'group coaching', '1:1 coaching',
                'coaching program', 'transformation coach', 'breakthrough coach',
                'mentor', 'mentoring', 'business mentor', 'entrepreneur coach',
                'startup coach', 'scale your business', 'grow your business'
            ],
            
            'author': [
                'author', 'writer', 'bestselling author', 'published author', 'book author',
                'novelist', 'non-fiction author', 'business author', 'self-help author',
                'amazon bestseller', 'wall street journal bestseller', 'usa today bestseller',
                'new york times bestseller', 'international bestseller', 'award-winning author',
                'published writer', 'book', 'books', 'my book', 'new book', 'latest book',
                'writing', 'writes about', 'kindle author', 'indie author', 'traditional author'
            ]
        }
        
        # Google search queries for each industry
        self.SEARCH_QUERIES = {
            'keynote_speaker': [
                'site:instagram.com "keynote speaker"',
                'site:instagram.com "motivational speaker"',
                'site:instagram.com "public speaker"',
                'site:instagram.com "conference speaker"',
                'site:instagram.com "professional speaker"',
                'site:instagram.com "tedx speaker"'
            ],
            'coach_consultant_creator': [
                'site:instagram.com "business coach"',
                'site:instagram.com "life coach"',
                'site:instagram.com "executive coach"',
                'site:instagram.com "course creator"',
                'site:instagram.com "online coach"',
                'site:instagram.com "consultant"'
            ],
            'author': [
                'site:instagram.com "author"',
                'site:instagram.com "bestselling author"',
                'site:instagram.com "published author"',
                'site:instagram.com "writer"',
                'site:instagram.com "book author"'
            ]
        }
    
    def extract_username_from_url(self, url: str) -> Optional[str]:
        """Extract username from Instagram URL"""
        pattern = r'https?://(?:www\.)?instagram\.com/([^/?#&]+)/?'
        match = re.match(pattern, url)
        
        if match:
            username = match.group(1)
            # Filter out non-username paths
            invalid_paths = {'p', 'reel', 'tv', 'stories', 'explore', 'accounts', 'direct'}
            if username not in invalid_paths and not username.startswith('p/'):
                return username
        return None
    
    def search_google_for_industry(self, industry: str, num_results: int = 30) -> List[str]:
        """Search Google for Instagram profiles in specific industry"""
        usernames = set()
        queries = self.SEARCH_QUERIES.get(industry, [])
        
        for query in queries:
            try:
                self.logger.info(f"Searching: {query}")
                
                # Add random delay between searches
                time.sleep(random.uniform(3, 6))
                
                search_results = search(
                    query,
                    num_results=num_results,
                    sleep_interval=random.uniform(2, 4),
                    lang='en'
                )
                
                for url in search_results:
                    username = self.extract_username_from_url(url)
                    if username:
                        usernames.add(username)
                        self.logger.debug(f"Found username: @{username}")
                
            except Exception as e:
                self.logger.error(f"Error searching '{query}': {e}")
                continue
        
        self.logger.info(f"Found {len(usernames)} unique usernames for {industry}")
        return list(usernames)
    
    def is_us_based(self, profile_data: Dict) -> bool:
        """Check if profile is US-based using extensive location detection"""
        text_to_check = ""
        
        # Check biography
        if profile_data.get('biography'):
            text_to_check += profile_data['biography'].lower() + " "
        
        # Check external URL
        if profile_data.get('external_url'):
            text_to_check += profile_data['external_url'].lower() + " "
        
        # Check full name
        if profile_data.get('full_name'):
            text_to_check += profile_data['full_name'].lower() + " "
        
        # Look for US indicators
        for indicator in self.US_INDICATORS:
            if indicator in text_to_check:
                self.logger.debug(f"US indicator found: '{indicator}' in profile")
                return True
        
        return False
    
    def classify_industry(self, profile_data: Dict) -> Optional[str]:
        """Classify profile into target industries"""
        text_to_check = ""
        
        # Combine biography and business category
        if profile_data.get('biography'):
            text_to_check += profile_data['biography'].lower() + " "
        
        if profile_data.get('business_category_name'):
            text_to_check += profile_data['business_category_name'].lower() + " "
        
        # Check against each industry
        industry_scores = {}
        
        for industry, keywords in self.INDUSTRY_KEYWORDS.items():
            score = 0
            for keyword in keywords:
                if keyword in text_to_check:
                    score += 1
            
            if score > 0:
                industry_scores[industry] = score
        
        # Return industry with highest score
        if industry_scores:
            best_industry = max(industry_scores.items(), key=lambda x: x[1])
            if best_industry[1] >= 1:  # At least 1 keyword match
                return best_industry[0]
        
        return None
    
    def extract_contact_info(self, profile_data: Dict) -> Dict:
        """Extract contact information from profile"""
        bio = profile_data.get('biography', '')
        url = profile_data.get('external_url', '')
        
        contact_info = {
            'email': None,
            'phone': None,
            'website': url if url else None
        }
        
        # Extract email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, bio)
        if email_match:
            contact_info['email'] = email_match.group(0)
        
        # Extract phone (US formats)
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',
            r'\+1[-.\s]?\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        ]
        
        for pattern in phone_patterns:
            phone_match = re.search(pattern, bio)
            if phone_match:
                contact_info['phone'] = phone_match.group(0)
                break
        
        return contact_info

    def verify_and_qualify_profile(self, username: str) -> Optional[Dict]:
        """Verify profile and check if it meets all qualification criteria"""
        try:
            # Add delay to be respectful
            time.sleep(random.uniform(2, 4))

            # Get profile data
            profile = instaloader.Profile.from_username(self.L.context, username)

            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'followees': profile.followees,
                'posts_count': profile.mediacount,
                'is_private': profile.is_private,
                'is_verified': profile.is_verified,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'business_category_name': getattr(profile, 'business_category_name', None)
            }

            # Apply qualification criteria

            # 1. Follower count check (100-10k sweet spot)
            if not (self.FOLLOWER_RANGE['min'] <= profile_data['followers'] <= self.FOLLOWER_RANGE['max']):
                self.logger.debug(f"@{username}: Followers ({profile_data['followers']}) outside range")
                return None

            # 2. Must be public profile
            if profile_data['is_private']:
                self.logger.debug(f"@{username}: Private account")
                return None

            # 3. Must have posts
            if profile_data['posts_count'] < 5:
                self.logger.debug(f"@{username}: Too few posts ({profile_data['posts_count']})")
                return None

            # 4. Must be US-based
            if not self.is_us_based(profile_data):
                self.logger.debug(f"@{username}: Not US-based")
                return None

            # 5. Must match target industry
            industry = self.classify_industry(profile_data)
            if not industry:
                self.logger.debug(f"@{username}: No target industry match")
                return None

            # 6. Check recent activity (get latest post timestamp)
            try:
                posts = profile.get_posts()
                latest_post = next(posts)
                days_since_post = (datetime.now() - latest_post.date_utc.replace(tzinfo=None)).days

                if days_since_post > self.ACTIVITY_THRESHOLD_DAYS:
                    self.logger.debug(f"@{username}: No recent activity ({days_since_post} days)")
                    return None

                profile_data['days_since_last_post'] = days_since_post

            except Exception as e:
                self.logger.debug(f"@{username}: Could not check recent activity: {e}")
                # Don't disqualify if we can't check activity
                profile_data['days_since_last_post'] = None

            # Add classification and contact info
            profile_data['classified_industry'] = industry
            profile_data['contact_info'] = self.extract_contact_info(profile_data)
            profile_data['qualification_score'] = self.calculate_qualification_score(profile_data)

            self.logger.info(f"✅ QUALIFIED: @{username} ({industry}, {profile_data['followers']} followers)")
            return profile_data

        except instaloader.exceptions.ProfileNotExistsException:
            self.logger.debug(f"@{username}: Profile does not exist")
            return None
        except Exception as e:
            self.logger.error(f"@{username}: Error verifying profile: {e}")
            return None

    def calculate_qualification_score(self, profile_data: Dict) -> int:
        """Calculate a qualification score (0-100) for the profile"""
        score = 0

        # Follower count score (0-25 points)
        followers = profile_data['followers']
        if 500 <= followers <= 5000:
            score += 25  # Sweet spot
        elif 100 <= followers <= 10000:
            score += 20  # Good range

        # Engagement potential (0-20 points)
        if profile_data.get('is_business_account'):
            score += 10
        if profile_data.get('is_verified'):
            score += 10

        # Content activity (0-20 points)
        posts = profile_data.get('posts_count', 0)
        if posts >= 100:
            score += 20
        elif posts >= 50:
            score += 15
        elif posts >= 20:
            score += 10

        # Contact availability (0-20 points)
        contact = profile_data.get('contact_info', {})
        if contact.get('email'):
            score += 10
        if contact.get('website'):
            score += 5
        if contact.get('phone'):
            score += 5

        # Recent activity (0-15 points)
        days_since_post = profile_data.get('days_since_last_post')
        if days_since_post is not None:
            if days_since_post <= 7:
                score += 15
            elif days_since_post <= 14:
                score += 10
            elif days_since_post <= 30:
                score += 5

        return min(score, 100)  # Cap at 100

    def generate_qualified_leads(self, industries: List[str], max_leads_per_industry: int = 20) -> Dict:
        """Generate qualified leads for specified industries"""
        all_leads = {}

        for industry in industries:
            self.logger.info(f"\n🎯 Searching for {industry.replace('_', ' ').title()} leads...")

            # Search Google for usernames
            usernames = self.search_google_for_industry(industry, num_results=50)

            if not usernames:
                self.logger.warning(f"No usernames found for {industry}")
                all_leads[industry] = []
                continue

            # Verify and qualify profiles
            qualified_leads = []
            checked_count = 0

            for username in usernames:
                if len(qualified_leads) >= max_leads_per_industry:
                    break

                checked_count += 1
                self.logger.info(f"[{checked_count}/{len(usernames)}] Checking @{username}...")

                qualified_profile = self.verify_and_qualify_profile(username)

                if qualified_profile:
                    qualified_leads.append(qualified_profile)
                    self.logger.info(f"   ✅ Qualified! Score: {qualified_profile['qualification_score']}/100")
                else:
                    self.logger.debug(f"   ❌ Not qualified")

                # Stop if we've checked enough profiles without finding leads
                if checked_count >= 100 and len(qualified_leads) == 0:
                    self.logger.warning(f"Checked 100 profiles for {industry}, no qualified leads found")
                    break

            all_leads[industry] = qualified_leads
            self.logger.info(f"Found {len(qualified_leads)} qualified {industry} leads")

        return all_leads

    def export_leads_to_excel(self, leads_data: Dict, filename: str = None) -> str:
        """Export qualified leads to Excel with separate sheets per industry"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"qualified_instagram_leads_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            summary_data = []

            for industry, leads in leads_data.items():
                if not leads:
                    continue

                # Prepare data for this industry
                industry_data = []
                for lead in leads:
                    contact = lead.get('contact_info', {})
                    industry_data.append({
                        'Username': f"@{lead['username']}",
                        'Full Name': lead.get('full_name', ''),
                        'Industry': lead.get('classified_industry', '').replace('_', ' ').title(),
                        'Followers': lead.get('followers', 0),
                        'Posts': lead.get('posts_count', 0),
                        'Qualification Score': lead.get('qualification_score', 0),
                        'Email': contact.get('email', ''),
                        'Phone': contact.get('phone', ''),
                        'Website': contact.get('website', ''),
                        'Is Business Account': lead.get('is_business_account', False),
                        'Is Verified': lead.get('is_verified', False),
                        'Days Since Last Post': lead.get('days_since_last_post', ''),
                        'Biography': lead.get('biography', '')[:200] + '...' if len(lead.get('biography', '')) > 200 else lead.get('biography', ''),
                        'Profile URL': f"https://instagram.com/{lead['username']}"
                    })

                # Create sheet for this industry
                df = pd.DataFrame(industry_data)
                sheet_name = industry.replace('_', ' ').title()[:31]  # Excel sheet name limit
                df.to_excel(writer, sheet_name=sheet_name, index=False)

                # Add to summary
                summary_data.append({
                    'Industry': sheet_name,
                    'Qualified Leads': len(leads),
                    'Avg Qualification Score': round(sum(lead.get('qualification_score', 0) for lead in leads) / len(leads), 1) if leads else 0,
                    'With Email': sum(1 for lead in leads if lead.get('contact_info', {}).get('email')),
                    'With Phone': sum(1 for lead in leads if lead.get('contact_info', {}).get('phone')),
                    'Business Accounts': sum(1 for lead in leads if lead.get('is_business_account')),
                    'Verified Accounts': sum(1 for lead in leads if lead.get('is_verified'))
                })

            # Create summary sheet
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

        self.logger.info(f"📊 Leads exported to: {filename}")
        return filename
