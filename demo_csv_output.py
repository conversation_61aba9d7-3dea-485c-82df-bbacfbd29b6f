#!/usr/bin/env python3
"""
Demo CSV Output - Shows what your CSV lead files will look like
"""

import csv
from datetime import datetime

def create_sample_csv():
    """Create a sample CSV file showing the format"""
    
    # Sample lead data (realistic examples)
    sample_leads = [
        {
            'username': 'keynote_speaker_pro',
            'full_name': '<PERSON>',
            'industry': 'Keynote Speaker',
            'followers': 4250,
            'posts': 187,
            'qualification_score': 85,
            'email': '<EMAIL>',
            'phone': '(*************',
            'website': 'https://sarahspeaks.com',
            'is_business_account': True,
            'is_verified': False,
            'days_since_last_post': 3,
            'biography': 'Professional keynote speaker helping Fortune 500 companies unlock leadership potential. Based in California. Available nationwide.',
            'profile_url': 'https://instagram.com/keynote_speaker_pro'
        },
        {
            'username': 'business_coach_mike',
            'full_name': '<PERSON>',
            'industry': 'Coach Consultant Creator',
            'followers': 2890,
            'posts': 156,
            'qualification_score': 78,
            'email': '<EMAIL>',
            'phone': '',
            'website': 'https://mikethompsoncoaching.com',
            'is_business_account': True,
            'is_verified': False,
            'days_since_last_post': 1,
            'biography': 'Business coach & consultant helping entrepreneurs scale from 6 to 7 figures. Texas-based. 1:1 coaching available.',
            'profile_url': 'https://instagram.com/business_coach_mike'
        },
        {
            'username': 'author_jane_smith',
            'full_name': 'Jane Smith',
            'industry': 'Author',
            'followers': 1650,
            'posts': 89,
            'qualification_score': 72,
            'email': '<EMAIL>',
            'phone': '******-987-6543',
            'website': 'https://janesmithbooks.com',
            'is_business_account': False,
            'is_verified': False,
            'days_since_last_post': 5,
            'biography': 'Bestselling author of 3 books on personal development. Writing coach helping aspiring authors get published. New York based.',
            'profile_url': 'https://instagram.com/author_jane_smith'
        },
        {
            'username': 'motivational_speaker_alex',
            'full_name': 'Alex Rodriguez',
            'industry': 'Keynote Speaker',
            'followers': 6750,
            'posts': 234,
            'qualification_score': 92,
            'email': '<EMAIL>',
            'phone': '',
            'website': 'https://alexrodriguezspeaks.com',
            'is_business_account': True,
            'is_verified': True,
            'days_since_last_post': 2,
            'biography': 'Motivational speaker & corporate trainer. TEDx speaker. Helping teams achieve breakthrough performance. Available nationwide.',
            'profile_url': 'https://instagram.com/motivational_speaker_alex'
        },
        {
            'username': 'life_coach_emma',
            'full_name': 'Emma Wilson',
            'industry': 'Coach Consultant Creator',
            'followers': 3420,
            'posts': 178,
            'qualification_score': 81,
            'email': '<EMAIL>',
            'phone': '(*************',
            'website': 'https://emmawilsoncoaching.com',
            'is_business_account': True,
            'is_verified': False,
            'days_since_last_post': 4,
            'biography': 'Certified life coach helping women create their dream careers. Online courses & 1:1 coaching. California-based, serving nationwide.',
            'profile_url': 'https://instagram.com/life_coach_emma'
        }
    ]
    
    # CSV headers
    headers = [
        'Username',
        'Full_Name', 
        'Industry',
        'Followers',
        'Posts',
        'Qualification_Score',
        'Email',
        'Phone',
        'Website',
        'Is_Business_Account',
        'Is_Verified',
        'Days_Since_Last_Post',
        'Biography',
        'Profile_URL'
    ]
    
    # Create CSV file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sample_instagram_leads_{timestamp}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write headers
        writer.writerow(headers)
        
        # Write lead data
        for lead in sample_leads:
            row = [
                f"@{lead['username']}",
                lead['full_name'],
                lead['industry'],
                lead['followers'],
                lead['posts'],
                lead['qualification_score'],
                lead['email'],
                lead['phone'],
                lead['website'],
                'Yes' if lead['is_business_account'] else 'No',
                'Yes' if lead['is_verified'] else 'No',
                lead['days_since_last_post'],
                lead['biography'],
                lead['profile_url']
            ]
            
            writer.writerow(row)
    
    return filename, sample_leads

def display_csv_preview(leads):
    """Display a preview of what the CSV contains"""
    print("📊 CSV LEAD FILE PREVIEW")
    print("=" * 80)
    print("This shows exactly what your CSV file will contain:")
    print()
    
    # Display in table format
    print(f"{'#':<2} {'Username':<25} {'Score':<6} {'Followers':<10} {'Email':<30}")
    print("-" * 80)
    
    for i, lead in enumerate(leads, 1):
        username = f"@{lead['username']}"
        score = f"{lead['qualification_score']}/100"
        followers = f"{lead['followers']:,}"
        email = lead['email'][:28] + "..." if len(lead['email']) > 30 else lead['email']
        
        print(f"{i:<2} {username:<25} {score:<6} {followers:<10} {email:<30}")
    
    print()
    print("📋 CSV COLUMNS INCLUDED:")
    columns = [
        "Username (Instagram handle)",
        "Full_Name (Display name)",
        "Industry (Speaker/Coach/Author)",
        "Followers (Count)",
        "Posts (Count)",
        "Qualification_Score (0-100)",
        "Email (Contact email)",
        "Phone (Contact phone)",
        "Website (External URL)",
        "Is_Business_Account (Yes/No)",
        "Is_Verified (Yes/No)",
        "Days_Since_Last_Post (Recency)",
        "Biography (Profile description)",
        "Profile_URL (Direct Instagram link)"
    ]
    
    for i, col in enumerate(columns, 1):
        print(f"{i:2}. {col}")

def show_lead_quality_stats(leads):
    """Show statistics about lead quality"""
    print(f"\n📊 LEAD QUALITY STATISTICS")
    print("=" * 50)
    
    total_leads = len(leads)
    with_email = sum(1 for lead in leads if lead['email'])
    with_phone = sum(1 for lead in leads if lead['phone'])
    business_accounts = sum(1 for lead in leads if lead['is_business_account'])
    verified_accounts = sum(1 for lead in leads if lead['is_verified'])
    
    avg_followers = sum(lead['followers'] for lead in leads) / total_leads
    avg_score = sum(lead['qualification_score'] for lead in leads) / total_leads
    
    print(f"Total Leads: {total_leads}")
    print(f"Average Followers: {avg_followers:,.0f}")
    print(f"Average Qualification Score: {avg_score:.1f}/100")
    print(f"Leads with Email: {with_email}/{total_leads} ({with_email/total_leads*100:.1f}%)")
    print(f"Leads with Phone: {with_phone}/{total_leads} ({with_phone/total_leads*100:.1f}%)")
    print(f"Business Accounts: {business_accounts}/{total_leads} ({business_accounts/total_leads*100:.1f}%)")
    print(f"Verified Accounts: {verified_accounts}/{total_leads} ({verified_accounts/total_leads*100:.1f}%)")

def show_outreach_recommendations(leads):
    """Show recommendations for outreach"""
    print(f"\n🎯 OUTREACH RECOMMENDATIONS")
    print("=" * 50)
    
    # Sort by qualification score
    sorted_leads = sorted(leads, key=lambda x: x['qualification_score'], reverse=True)
    
    print("Priority Order (start with highest scores):")
    for i, lead in enumerate(sorted_leads, 1):
        email_status = "📧" if lead['email'] else "❌"
        business_status = "🏢" if lead['is_business_account'] else "👤"
        verified_status = "✅" if lead['is_verified'] else ""
        
        print(f"{i}. @{lead['username']} (Score: {lead['qualification_score']}/100) {email_status} {business_status} {verified_status}")
    
    print(f"\n💡 OUTREACH TIPS:")
    print("• Start with leads scoring 80+ points")
    print("• Prioritize leads with email addresses (📧)")
    print("• Business accounts (🏢) typically have higher response rates")
    print("• Verified accounts (✅) are high-value targets")
    print("• Mention their recent posts to show genuine interest")
    print("• Reference their location/industry for personalization")

def main():
    """Create sample CSV and show preview"""
    print("🚀 INSTAGRAM LEAD CSV GENERATOR - DEMO")
    print("=" * 60)
    print("This demonstrates the exact format of your CSV lead files")
    print()
    
    # Create sample CSV
    filename, leads = create_sample_csv()
    
    print(f"✅ Sample CSV created: {filename}")
    print()
    
    # Show preview
    display_csv_preview(leads)
    
    # Show statistics
    show_lead_quality_stats(leads)
    
    # Show outreach recommendations
    show_outreach_recommendations(leads)
    
    print(f"\n🎉 YOUR REAL CSV FILES WILL HAVE THIS EXACT FORMAT!")
    print(f"📄 Sample file created: {filename}")
    print(f"\n🚀 To generate real leads, run:")
    print(f"   python auto_csv_generator.py")
    print(f"   python run_targeted_leads.py --test-mode")
    
    print(f"\n💡 CSV files can be opened in:")
    print(f"   • Microsoft Excel")
    print(f"   • Google Sheets")
    print(f"   • Any CRM system")
    print(f"   • Email marketing tools")

if __name__ == "__main__":
    main()
