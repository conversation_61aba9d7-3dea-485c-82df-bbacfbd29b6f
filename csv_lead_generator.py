#!/usr/bin/env python3
"""
CSV Lead Generator - Exports Instagram leads directly to CSV format
Perfect for Excel, Google Sheets, or any CRM system
"""

import csv
import time
import random
from datetime import datetime
from targeted_lead_generator import TargetedLeadGenerator

def export_leads_to_csv(leads, filename=None):
    """Export leads to CSV format"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"instagram_leads_{timestamp}.csv"
    
    # CSV headers
    headers = [
        'Username',
        'Full_Name', 
        'Industry',
        'Followers',
        'Posts',
        'Qualification_Score',
        'Email',
        'Phone',
        'Website',
        'Is_Business_Account',
        'Is_Verified',
        'Days_Since_Last_Post',
        'Biography',
        'Profile_URL'
    ]
    
    # Write CSV file
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write headers
        writer.writerow(headers)
        
        # Write lead data
        for lead in leads:
            contact = lead.get('contact_info', {})
            
            row = [
                f"@{lead['username']}",
                lead.get('full_name', ''),
                lead.get('classified_industry', '').replace('_', ' ').title(),
                lead.get('followers', 0),
                lead.get('posts_count', 0),
                lead.get('qualification_score', 0),
                contact.get('email', ''),
                contact.get('phone', ''),
                contact.get('website', ''),
                'Yes' if lead.get('is_business_account') else 'No',
                'Yes' if lead.get('is_verified') else 'No',
                lead.get('days_since_last_post', ''),
                lead.get('biography', '').replace('\n', ' ').replace('\r', ' ')[:200],
                f"https://instagram.com/{lead['username']}"
            ]
            
            writer.writerow(row)
    
    print(f"📊 CSV exported to: {filename}")
    return filename

def quick_csv_search(industry='keynote_speaker', max_leads=10):
    """Quick search that exports directly to CSV"""
    print(f"🎯 SEARCHING FOR {industry.replace('_', ' ').upper()} LEADS")
    print("=" * 60)
    print("Criteria: 100-10k followers, US-based, recent activity, public accounts")
    print()
    
    generator = TargetedLeadGenerator()
    
    # Search for usernames
    print("Step 1: Searching Google for Instagram profiles...")
    usernames = generator.search_google_for_industry(industry, num_results=30)
    
    if not usernames:
        print("❌ No usernames found. Try again later.")
        return []
    
    print(f"✅ Found {len(usernames)} potential usernames")
    print("Sample usernames:", usernames[:5])
    print()
    
    # Verify and qualify profiles
    print("Step 2: Verifying profiles and applying qualification criteria...")
    qualified_leads = []
    
    for i, username in enumerate(usernames[:20], 1):  # Check first 20
        print(f"[{i}/20] Checking @{username}...", end=' ')
        
        try:
            qualified_profile = generator.verify_and_qualify_profile(username)
            
            if qualified_profile:
                qualified_leads.append(qualified_profile)
                score = qualified_profile.get('qualification_score', 0)
                followers = qualified_profile.get('followers', 0)
                contact = qualified_profile.get('contact_info', {})
                email_status = "📧" if contact.get('email') else "❌"
                
                print(f"✅ QUALIFIED! Score: {score}/100, Followers: {followers:,} {email_status}")
                
                if len(qualified_leads) >= max_leads:
                    print(f"🎯 Found {max_leads} qualified leads - stopping search")
                    break
            else:
                print("❌ Not qualified")
                
        except Exception as e:
            print(f"⚠️ Error: {str(e)[:50]}...")
            continue
    
    print()
    print("=" * 60)
    print(f"📊 RESULTS: Found {len(qualified_leads)} qualified leads")
    print("=" * 60)
    
    if qualified_leads:
        # Sort by qualification score
        qualified_leads.sort(key=lambda x: x.get('qualification_score', 0), reverse=True)
        
        # Display top results
        print("\n🌟 TOP QUALIFIED LEADS:")
        for i, lead in enumerate(qualified_leads[:5], 1):
            contact = lead.get('contact_info', {})
            print(f"\n{i}. @{lead['username']} (Score: {lead.get('qualification_score', 0)}/100)")
            print(f"   👤 {lead.get('full_name', 'N/A')}")
            print(f"   👥 {lead.get('followers', 0):,} followers")
            print(f"   📧 {contact.get('email', 'No email')}")
            print(f"   🌐 {contact.get('website', 'No website')}")
            print(f"   📝 {lead.get('biography', '')[:80]}...")
        
        # Export to CSV
        csv_file = export_leads_to_csv(qualified_leads)
        
        print(f"\n✅ SUCCESS! {len(qualified_leads)} qualified leads exported to CSV")
        print(f"📄 File: {csv_file}")
        
        # Show stats
        with_email = sum(1 for lead in qualified_leads if lead.get('contact_info', {}).get('email'))
        business_accounts = sum(1 for lead in qualified_leads if lead.get('is_business_account'))
        avg_score = sum(lead.get('qualification_score', 0) for lead in qualified_leads) / len(qualified_leads)
        
        print(f"\n📊 LEAD QUALITY STATS:")
        print(f"   • Average qualification score: {avg_score:.1f}/100")
        print(f"   • Leads with email: {with_email}/{len(qualified_leads)}")
        print(f"   • Business accounts: {business_accounts}/{len(qualified_leads)}")
        
        return qualified_leads
    else:
        print("❌ No qualified leads found in this search")
        return []

def main():
    """Main CSV lead generation function"""
    print("📊 CSV INSTAGRAM LEAD GENERATOR")
    print("=" * 60)
    print("Generates qualified leads and exports directly to CSV format")
    print("Perfect for Excel, Google Sheets, or CRM import")
    print()
    
    # Menu options
    print("Choose your target industry:")
    print("1. Keynote Speakers")
    print("2. Business Coaches/Consultants/Course Creators")
    print("3. Authors")
    print("4. All Industries (separate CSV files)")
    print()
    
    try:
        choice = input("Enter your choice (1-4): ").strip()
        
        if choice == '1':
            leads = quick_csv_search('keynote_speaker', max_leads=10)
        elif choice == '2':
            leads = quick_csv_search('coach_consultant_creator', max_leads=10)
        elif choice == '3':
            leads = quick_csv_search('author', max_leads=10)
        elif choice == '4':
            print("Searching all industries...")
            all_leads = []
            
            for industry in ['keynote_speaker', 'coach_consultant_creator', 'author']:
                print(f"\n{'='*60}")
                leads = quick_csv_search(industry, max_leads=5)
                all_leads.extend(leads)
                
                if leads:
                    print(f"Waiting 30 seconds before next search...")
                    time.sleep(30)
            
            if all_leads:
                # Export combined CSV
                combined_csv = export_leads_to_csv(all_leads)
                print(f"\n🎉 COMBINED RESULTS: {len(all_leads)} total leads")
                print(f"📄 Combined CSV: {combined_csv}")
        else:
            print("Invalid choice. Please run again and select 1-4.")
            return
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Open the CSV file in Excel or Google Sheets")
        print(f"2. Sort by 'Qualification_Score' (highest first)")
        print(f"3. Filter for leads with email addresses")
        print(f"4. Start your outreach with the highest-scoring leads")
        print(f"5. Focus on business accounts for better response rates")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Search interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
